import { create } from 'zustand';

interface User {
	id: string;
	email: string;
	name: string | null;
	userType: "basic" | "unlimited-lite" | "unlimited-premium" | "agency-basic" | "agency-deluxe" | "admin";
}

interface UsersStore {
	users: User[];
	userCount: number;
	isLoading: boolean;
	error: string | null;
	setUsers: (users: User[]) => void;
	setLoading: (loading: boolean) => void;
	setError: (error: string | null) => void;
}

export const useUsersStore = create<UsersStore>((set) => ({
	users: [],
	userCount: 0,
	isLoading: false,
	error: null,
	setUsers: (users) => set({ users, userCount: users.length }),
	setLoading: (loading) => set({ isLoading: loading }),
	setError: (error) => set({ error }),
}));