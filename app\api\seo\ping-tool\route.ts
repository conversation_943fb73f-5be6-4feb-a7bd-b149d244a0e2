import { NextResponse } from "next/server";

export async function POST(req: Request) {
	try {
		const { url } = await req.json();
		const startTime = Date.now();
		
		try {
			const response = await fetch(url, {
				method: 'HEAD',
				headers: {
					'User-Agent': 'SEO-Tool-Ping-Check',
				},
			});

			const endTime = Date.now();
			const responseTime = endTime - startTime;

			const result = {
				url,
				status: response.ok ? 'Success' : 'Failed',
				responseTime,
				timestamp: new Date().toLocaleString(),
			};

			return NextResponse.json({ result });
		} catch (error) {
            console.error("An error occurred:", error);
			return NextResponse.json({
				result: {
					url,
					status: 'Failed',
					responseTime: Date.now() - startTime,
					timestamp: new Date().toLocaleString(),
				},
			});
		}
	} catch (error) {
		console.error("Ping tool error:", error);
		return NextResponse.json(
			{ error: "Failed to ping website" },
			{ status: 500 }
		);
	}
}