import { togetherai } from '@ai-sdk/togetherai';
import { streamText } from 'ai';
import { NextResponse } from 'next/server';

export async function POST(req: Request) {
	try {
		const { messages } = await req.json();
		
		// Convert chat messages to a prompt format
		const prompt = messages
			.map((msg: { role: string; content: string }) => 
				`${msg.role === 'user' ? 'Human' : 'Assistant'}: ${msg.content}`
			)
			.join('\n') + '\nAssistant:';

		const stream = await streamText({
			model: togetherai('meta-llama/Llama-3.3-70B-Instruct-Turbo-Free'),
			prompt,
			// maxTokens: 8000,
			// temperature: 0.7,
			// topP: 0.7,
			// topK: 50,
			// frequencyPenalty: 1.0,
			// presencePenalty: 0.0,
			// stop: ["<|end▁of▁sentence|>"]
		});

		return new Response(stream.textStream, {
			headers: {
				'Content-Type': 'text/event-stream',
				'Cache-Control': 'no-cache',
				'Connection': 'keep-alive',
			},
		});
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
	} catch (error: any) {
		console.error('DeepSeek V3 API Error:', error);
		return NextResponse.json({ error: error.message }, { status: 500 });
	}
}
