"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Loader2 } from "lucide-react";
import { Card } from "@/components/ui/card";

interface PingResult {
	url: string;
	status: string;
	responseTime: number;
	timestamp: string;
}

export default function PingToolPage() {
	const [url, setUrl] = useState("");
	const [loading, setLoading] = useState(false);
	const [results, setResults] = useState<PingResult[]>([]);
	const { toast } = useToast();

	const handlePing = async () => {
		if (!url.trim()) {
			toast({
				title: "Error",
				description: "Please enter a website URL",
				variant: "destructive",
			});
			return;
		}

		try {
			setLoading(true);
			const response = await fetch("/api/seo/ping-tool", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ url }),
			});

			if (!response.ok) {
				throw new Error("Failed to ping website");
			}

			const data = await response.json();
			setResults(prev => [data.result, ...prev].slice(0, 10)); // Keep last 10 results
		} catch (error) {
            console.error("An error occurred:", error);
			toast({
				title: "Error",
				description: "Failed to ping website. Please try again.",
				variant: "destructive",
			});
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="container mx-auto py-8">
			<h1 className="text-3xl font-bold mb-6">Website Ping Tool</h1>
			<div className="grid gap-6">
				<div className="flex gap-4">
					<Input
						placeholder="Enter website URL"
						value={url}
						onChange={(e) => setUrl(e.target.value)}
						className="flex-1"
					/>
					<Button 
						onClick={handlePing} 
						disabled={loading}
					>
						{loading ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								Pinging...
							</>
						) : (
							"Ping Website"
						)}
					</Button>
				</div>
				
				{results.length > 0 && (
					<div className="space-y-4">
						<h2 className="text-xl font-semibold">Recent Results</h2>
						{results.map((result, index) => (
							<Card key={index} className="p-4">
								<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
									<div>
										<h3 className="font-medium">URL</h3>
										<p className="text-sm truncate">{result.url}</p>
									</div>
									<div>
										<h3 className="font-medium">Status</h3>
										<p className={`text-sm ${result.status === 'Success' ? 'text-green-600' : 'text-red-600'}`}>
											{result.status}
										</p>
									</div>
									<div>
										<h3 className="font-medium">Response Time</h3>
										<p className="text-sm">{result.responseTime}ms</p>
									</div>
									<div>
										<h3 className="font-medium">Timestamp</h3>
										<p className="text-sm">{result.timestamp}</p>
									</div>
								</div>
							</Card>
						))}
					</div>
				)}
			</div>
		</div>
	);
}