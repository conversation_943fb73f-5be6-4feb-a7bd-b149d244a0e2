"use client";

import { useState } from "react";
import { Code2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

export default function URLEncoderPage() {
	const [input, setInput] = useState("");
	const [output, setOutput] = useState("");

	const handleEncode = () => {
		try {
			setOutput(encodeURIComponent(input));
		} catch (error) {
            console.log(error);
			setOutput("Error encoding URL");
		}
	};

	const handleDecode = () => {
		try {
			setOutput(decodeURIComponent(input));
		} catch (error) {
            console.log(error);
			setOutput("Error decoding URL");
		}
	};

	return (
		<div className="container mx-auto py-8">
			<div className="flex flex-col gap-6">
				<div>
					<h1 className="text-4xl font-bold gradient-text mb-2">URL Encoder/Decoder</h1>
					<p className="text-muted-foreground">
						Encode or decode URLs for web safe format
					</p>
				</div>

				<Card className="p-6">
					<Tabs defaultValue="encode" className="w-full">
						<TabsList className="grid w-full grid-cols-2">
							<TabsTrigger value="encode">Encode</TabsTrigger>
							<TabsTrigger value="decode">Decode</TabsTrigger>
						</TabsList>
						<TabsContent value="encode">
							<div className="flex flex-col gap-4">
								<Textarea
									placeholder="Enter text to encode"
									value={input}
									onChange={(e) => setInput(e.target.value)}
									rows={5}
								/>
								<Button onClick={handleEncode}>
									<Code2 className="mr-2 h-4 w-4" />
									Encode URL
								</Button>
							</div>
						</TabsContent>
						<TabsContent value="decode">
							<div className="flex flex-col gap-4">
								<Textarea
									placeholder="Enter text to decode"
									value={input}
									onChange={(e) => setInput(e.target.value)}
									rows={5}
								/>
								<Button onClick={handleDecode}>
									<Code2 className="mr-2 h-4 w-4" />
									Decode URL
								</Button>
							</div>
						</TabsContent>
					</Tabs>

					{output && (
						<div className="mt-4">
							<label className="block text-sm font-medium mb-2">Result</label>
							<Textarea value={output} readOnly rows={5} />
						</div>
					)}
				</Card>
			</div>
		</div>
	);
}