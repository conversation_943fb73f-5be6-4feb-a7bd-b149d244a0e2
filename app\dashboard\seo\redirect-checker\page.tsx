"use client";

import { useState } from "react";
import { <PERSON>Check } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface RedirectResult {
	url: string;
	statusCode: number;
	redirectUrl?: string;
}

export default function RedirectCheckerPage() {
	const [url, setUrl] = useState("");
	const [loading, setLoading] = useState(false);
	const [results, setResults] = useState<RedirectResult[]>([]);
	const [error, setError] = useState("");

	const checkRedirect = async () => {
		setLoading(true);
		setError("");
		setResults([]);

		try {
			const response = await fetch("/api/check-redirect", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ url }),
			});

			if (!response.ok) {
				throw new Error("Failed to check redirect");
			}

			const data = await response.json();
			setResults(data.redirects);
		} catch (err) {
			setError(err instanceof Error ? err.message : "An error occurred");
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="container mx-auto py-8">
			<div className="flex flex-col gap-6">
				<div>
					<h1 className="text-4xl font-bold gradient-text mb-2">WWW Redirect Checker</h1>
					<p className="text-muted-foreground">
						Test website redirects and analyze redirect chains
					</p>
				</div>

				<Card className="p-6">
					<div className="flex flex-col gap-4">
						<div>
							<label className="block text-sm font-medium mb-2">Website URL</label>
							<div className="flex gap-2">
								<Input
									placeholder="Enter URL (e.g., http://example.com)"
									value={url}
									onChange={(e) => setUrl(e.target.value)}
								/>
								<Button onClick={checkRedirect} disabled={loading}>
									<MonitorCheck className="mr-2 h-4 w-4" />
									{loading ? "Checking..." : "Check Redirects"}
								</Button>
							</div>
						</div>

						{error && (
							<Alert variant="destructive">
								<AlertDescription>{error}</AlertDescription>
							</Alert>
						)}

						{results.length > 0 && (
							<div className="space-y-4">
								<h2 className="text-xl font-semibold">Redirect Chain</h2>
								{results.map((result, index) => (
									<Card key={index} className="p-4">
										<div className="flex flex-col gap-2">
											<div className="flex items-center justify-between">
												<span className="font-medium">URL:</span>
												<span className="text-muted-foreground">{result.url}</span>
											</div>
											<div className="flex items-center justify-between">
												<span className="font-medium">Status Code:</span>
												<span className="text-muted-foreground">{result.statusCode}</span>
											</div>
											{result.redirectUrl && (
												<div className="flex items-center justify-between">
													<span className="font-medium">Redirects To:</span>
													<span className="text-muted-foreground">{result.redirectUrl}</span>
												</div>
											)}
										</div>
									</Card>
								))}
							</div>
						)}
					</div>
				</Card>
			</div>
		</div>
	);
}