"use client";

import Image from "next/image";
import { ThemeLogo } from "./theme-logo";
import { useTheme } from "next-themes";

interface AuthLayoutProps {
  children: React.ReactNode;
  illustration?: "login" | "register";
  centerOnly?: boolean;
}

export function AuthLayout({
  children,
  illustration,
  centerOnly = false,
}: AuthLayoutProps) {
  const { theme } = useTheme();
  const isDark = theme === "dark";
  const isLogin = illustration === "login";

  if (centerOnly) {
    return (
      <div className="min-h-screen flex items-center justify-center relative overflow-hidden bg-background">
        {/* Enhanced background with multiple gradients */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 dark:from-blue-950/20 dark:via-purple-950/20 dark:to-pink-950/20" />
          <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-blue-500/5 to-purple-500/10 dark:via-blue-400/5 dark:to-purple-400/10" />
          <div className="absolute top-0 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse" />
          <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000" />
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-pink-500/5 rounded-full blur-3xl animate-pulse delay-2000" />
        </div>

        <div className="relative w-full max-w-md mx-auto px-4">
          <div className="glass-card p-8 sm:p-10 backdrop-blur-xl bg-background/40 dark:bg-background/20 shadow-[0_8px_30px_rgb(0,0,0,0.12)] dark:shadow-[0_8px_30px_rgba(0,0,0,0.5)] border border-white/20 dark:border-white/10 rounded-2xl">
            <div className="mb-8">
              <div className="flex justify-center mb-8">
                <ThemeLogo />
              </div>
              {children}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex relative overflow-hidden bg-background">
      {/* Left side - Form */}
      <div className="w-full lg:w-1/2 flex flex-col items-center justify-center relative py-6 sm:py-12 px-4">
        <div className="absolute inset-0 auth-gradient opacity-20" />
        <div className="relative w-full">
          <div className="max-w-md w-full mx-auto">
            <div className="glass-card p-8 sm:p-10 backdrop-blur-xl bg-background/30 shadow-[0_8px_30px_rgb(0,0,0,0.12)] dark:shadow-[0_8px_30px_rgba(0,0,0,0.5)] border border-white/10 rounded-2xl">
              <div className="mb-8">
                <div className="flex justify-center mb-8">
                  <ThemeLogo />
                </div>
                {children}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right side - Illustration */}
      <div className="hidden lg:block lg:w-1/2 relative">
        {!isLogin && <div className="absolute inset-0 bg-background/95" />}
        <div className="absolute inset-0">
          <Image
            src={`/${illustration}.png`}
            alt={`${illustration} illustration`}
            fill
            className={
              isLogin ? "object-cover object-center" : "object-contain p-12"
            }
            style={{
              filter: isDark
                ? "brightness(0.8) contrast(1.2)"
                : "brightness(0.95) contrast(1.1)",
              mixBlendMode: isDark ? "lighten" : "darken",
            }}
            priority
            sizes="(min-width: 1024px) 50vw, 100vw"
          />
        </div>
      </div>
    </div>
  );
}
