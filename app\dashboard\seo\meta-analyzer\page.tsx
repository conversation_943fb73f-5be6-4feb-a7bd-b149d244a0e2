"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Loader2 } from "lucide-react";

export default function MetaAnalyzerPage() {
	const [url, setUrl] = useState("");
	const [loading, setLoading] = useState(false);
	const [result, setResult] = useState<{
		title: { value: string; score: number; suggestions: string[] };
		description: { value: string; score: number; suggestions: string[] };
		keywords: { value: string[]; score: number; suggestions: string[] };
		overall: number;
	} | null>(null);
	const { toast } = useToast();

	const handleAnalyze = async () => {
		if (!url.trim()) {
			toast({
				title: "Error",
				description: "Please enter a URL to analyze",
				variant: "destructive",
			});
			return;
		}

		try {
			setLoading(true);
			const response = await fetch("/api/seo/meta-analyzer", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ url }),
			});

			if (!response.ok) {
				throw new Error("Failed to analyze meta tags");
			}

			const data = await response.json();
			setResult(data.result);
		} catch (error) {
            console.error("An error occurred:", error);
			toast({
				title: "Error",
				description: "Failed to analyze meta tags. Please try again.",
				variant: "destructive",
			});
		} finally {
			setLoading(false);
		}
	};

	const getScoreColor = (score: number) => {
		if (score >= 80) return "text-green-600";
		if (score >= 60) return "text-yellow-600";
		return "text-red-600";
	};

	return (
		<div className="container mx-auto py-8">
			<h1 className="text-3xl font-bold mb-6">Meta Tag Analyzer</h1>
			<div className="grid gap-6">
				<div>
					<label className="block mb-2">Website URL</label>
					<Input
						placeholder="Enter website URL to analyze"
						value={url}
						onChange={(e) => setUrl(e.target.value)}
					/>
				</div>
				<Button 
					onClick={handleAnalyze} 
					disabled={loading}
					className="w-full md:w-auto"
				>
					{loading ? (
						<>
							<Loader2 className="mr-2 h-4 w-4 animate-spin" />
							Analyzing...
						</>
					) : (
						"Analyze Meta Tags"
					)}
				</Button>
				{result && (
					<div className="space-y-6">
						<div className="p-4 border rounded-lg">
							<h2 className="text-xl font-semibold mb-4">Analysis Results</h2>
							<div className="text-lg mb-4">
								Overall Score: 
								<span className={getScoreColor(result.overall)}>
									{result.overall}%
								</span>
							</div>

							<div className="space-y-6">
								<div>
									<h3 className="font-medium mb-2">Meta Title</h3>
									<p className="mb-1">{result.title.value}</p>
									<p className={`mb-2 ${getScoreColor(result.title.score)}`}>
										Score: {result.title.score}%
									</p>
									{result.title.suggestions.length > 0 && (
										<ul className="list-disc pl-5 text-sm text-muted-foreground">
											{result.title.suggestions.map((suggestion, index) => (
												<li key={index}>{suggestion}</li>
											))}
										</ul>
									)}
								</div>

								<div>
									<h3 className="font-medium mb-2">Meta Description</h3>
									<p className="mb-1">{result.description.value}</p>
									<p className={`mb-2 ${getScoreColor(result.description.score)}`}>
										Score: {result.description.score}%
									</p>
									{result.description.suggestions.length > 0 && (
										<ul className="list-disc pl-5 text-sm text-muted-foreground">
											{result.description.suggestions.map((suggestion, index) => (
												<li key={index}>{suggestion}</li>
											))}
										</ul>
									)}
								</div>

								<div>
									<h3 className="font-medium mb-2">Keywords</h3>
									<p className="mb-1">{result.keywords.value.join(", ")}</p>
									<p className={`mb-2 ${getScoreColor(result.keywords.score)}`}>
										Score: {result.keywords.score}%
									</p>
									{result.keywords.suggestions.length > 0 && (
										<ul className="list-disc pl-5 text-sm text-muted-foreground">
											{result.keywords.suggestions.map((suggestion, index) => (
												<li key={index}>{suggestion}</li>
											))}
										</ul>
									)}
								</div>
							</div>
						</div>
					</div>
				)}
			</div>
		</div>
	);
}