import { GoogleGenerativeAI } from "@google/generative-ai";
import { NextResponse } from "next/server";

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);

export async function GET() {
	try {
		const model = genAI.getGenerativeModel({ model: "gemini-pro" });
		
		const prompt = `Generate realistic IP address information in this exact JSON format without any markdown formatting or additional text:
		{
			"ip": "valid IP address",
			"country": "country name",
			"region": "region/state name",
			"city": "city name",
			"isp": "internet service provider name",
			"timezone": "timezone string",
			"latitude": number,
			"longitude": number
		}`;

		const result = await model.generateContent(prompt);
		const response = await result.response;
		const text = response.text();
		
		// Parse the response as JSON
		const jsonResult = JSON.parse(text.replace(/```json\n|```/g, '').trim());

		return NextResponse.json({ result: jsonResult });
	} catch (error) {
		console.error("IP address info error:", error);
		return NextResponse.json(
			{ error: "Failed to fetch IP information" },
			{ status: 500 }
		);
	}
}