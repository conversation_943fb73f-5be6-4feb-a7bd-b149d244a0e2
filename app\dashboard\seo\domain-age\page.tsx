"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Calendar, Globe } from "lucide-react";
import { Card } from "@/components/ui/card";

interface DomainAgeResult {
  domain: string;
  creationDate: string;
  age: {
    years: number;
    months: number;
    days: number;
  };
  expiryDate: string;
  registrar: string;
  statuses: string[];
}

export default function DomainAgeCheckerPage() {
  const [domain, setDomain] = useState("");
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<DomainAgeResult | null>(null);
  const { toast } = useToast();

  const handleCheck = async () => {
    if (!domain.trim()) {
      toast({
        title: "Error",
        description: "Please enter a domain name",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);
      const response = await fetch("/api/seo/domain-age", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ domain }),
      });

      if (!response.ok) {
        throw new Error("Failed to check domain age");
      }

      const data = await response.json();
      setResult(data.result);
    } catch (err) {
      console.error("An error occurred:", err);
      toast({
        title: "Error",
        description: "Failed to check domain age. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Domain Age Checker</h1>
      <div className="grid gap-6">
        <div>
          <label className="block mb-2">Domain Name</label>
          <Input
            type="text"
            placeholder="Enter domain name (e.g., example.com)"
            value={domain}
            onChange={(e) => setDomain(e.target.value)}
          />
        </div>
        <Button
          onClick={handleCheck}
          disabled={loading}
          className="w-full md:w-auto"
        >
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Checking...
            </>
          ) : (
            "Check Domain Age"
          )}
        </Button>

        {result && (
          <div className="space-y-6">
            <Card className="p-6">
              <div className="flex items-center gap-3 mb-6">
                <Globe className="h-6 w-6 text-primary" />
                <h2 className="text-2xl font-semibold">{result.domain}</h2>
              </div>

              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <div className="p-4 bg-secondary/20 rounded-lg">
                    <h3 className="font-medium mb-2">Domain Age</h3>
                    <p className="text-2xl font-bold text-primary">
                      {result.age?.years || 0} years, {result.age?.months || 0}{" "}
                      months
                    </p>
                    <p className="text-sm text-muted-foreground">
                      and {result.age?.days || 0} days
                    </p>
                  </div>

                  <div className="p-4 bg-secondary/20 rounded-lg">
                    <h3 className="font-medium mb-2">Registration Date</h3>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      <p>
                        {new Date(result.creationDate).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="p-4 bg-secondary/20 rounded-lg">
                    <h3 className="font-medium mb-2">Expiry Date</h3>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      <p>{new Date(result.expiryDate).toLocaleDateString()}</p>
                    </div>
                  </div>

                  <div className="p-4 bg-secondary/20 rounded-lg">
                    <h3 className="font-medium mb-2">Registrar</h3>
                    <p>{result.registrar || "Unknown"}</p>
                  </div>

                  <div className="p-4 bg-secondary/20 rounded-lg">
                    <h3 className="font-medium mb-2">Domain Status</h3>
                    <div className="flex flex-wrap gap-2">
                      {(result.statuses || ["Unknown"]).map((status, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-primary/10 rounded-full text-sm"
                        >
                          {status}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}
