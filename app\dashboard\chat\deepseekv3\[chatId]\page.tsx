"use client";

import { DeepSeekV3ChatContainer } from "@/components/chat/deepseekv3-chat-container";
import { useParams } from "next/navigation";

export default function DeepSeekV3ChatWithId() {
	const params = useParams();
	const chatId = params?.chatId as string;

	return (
		<div className="flex-1 space-y-6">
			<div className="container h-[calc(100vh-4rem)]">
				<DeepSeekV3ChatContainer id={chatId} />
			</div>
		</div>
	);
}
