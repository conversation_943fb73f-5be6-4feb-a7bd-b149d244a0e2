"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { Loader2 } from "lucide-react";

export default function ArticleRewriterPage() {
	const [content, setContent] = useState("");
	const [result, setResult] = useState("");
	const [loading, setLoading] = useState(false);
	const { toast } = useToast();

	const handleRewrite = async () => {
		if (!content.trim()) {
			toast({
				title: "Error",
				description: "Please enter some content to rewrite",
				variant: "destructive",
			});
			return;
		}

		try {
			setLoading(true);
			const response = await fetch("/api/seo/article-rewriter", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ content }),
			});

			if (!response.ok) {
				throw new Error("Failed to rewrite article");
			}

			const data = await response.json();
			setResult(data.result);
		} catch (error) {
			console.error("An error occurred:", error);
			toast({
				title: "Error",
				description: "Failed to rewrite article. Please try again.",
				variant: "destructive",
			});
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="container mx-auto py-8">
			<h1 className="text-3xl font-bold mb-6">Article Rewriter</h1>
			<div className="grid gap-6">
				<div>
					<label className="block mb-2">Original Content</label>
					<Textarea
						placeholder="Enter your article content here..."
						value={content}
						onChange={(e) => setContent(e.target.value)}
						rows={8}
					/>
				</div>
				<Button 
					onClick={handleRewrite} 
					disabled={loading}
					className="w-full md:w-auto"
				>
					{loading ? (
						<>
							<Loader2 className="mr-2 h-4 w-4 animate-spin" />
							Rewriting...
						</>
					) : (
						"Rewrite Article"
					)}
				</Button>
				{result && (
					<div>
						<label className="block mb-2">Rewritten Content</label>
						<Textarea value={result} rows={8} readOnly />
					</div>
				)}
			</div>
		</div>
	);
}

