"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { Loader2 } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";

export default function RobotsGeneratorPage() {
	const [siteUrl, setSiteUrl] = useState("");
	const [allowAll, setAllowAll] = useState(false);
	const [disallowPaths, setDisallowPaths] = useState("");
	const [sitemap, setSitemap] = useState("");
	const [loading, setLoading] = useState(false);
	const [result, setResult] = useState("");
	const { toast } = useToast();

	const handleGenerate = async () => {
		if (!siteUrl.trim()) {
			toast({
				title: "Error",
				description: "Please enter your website URL",
				variant: "destructive",
			});
			return;
		}

		try {
			setLoading(true);
			const response = await fetch("/api/seo/robots-generator", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					siteUrl,
					allowAll,
					disallowPaths,
					sitemap,
				}),
			});

			if (!response.ok) {
				throw new Error("Failed to generate robots.txt");
			}

			const data = await response.json();
			setResult(data.result);
		} catch (error) {
            console.error("An error occurred:", error);
			toast({
				title: "Error",
				description: "Failed to generate robots.txt. Please try again.",
				variant: "destructive",
			});
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="container mx-auto py-8">
			<h1 className="text-3xl font-bold mb-6">Robots.txt Generator</h1>
			<div className="grid gap-6">
				<div>
					<label className="block mb-2">Website URL</label>
					<Input
						placeholder="Enter your website URL"
						value={siteUrl}
						onChange={(e) => setSiteUrl(e.target.value)}
					/>
				</div>
				<div className="flex items-center space-x-2">
					<Checkbox
						id="allowAll"
						checked={allowAll}
						onCheckedChange={(checked) => setAllowAll(checked as boolean)}
					/>
					<label htmlFor="allowAll">Allow all robots to crawl</label>
				</div>
				<div>
					<label className="block mb-2">Disallow Paths (one per line)</label>
					<Textarea
						placeholder="/admin/&#10;/private/&#10;/temp/"
						value={disallowPaths}
						onChange={(e) => setDisallowPaths(e.target.value)}
						rows={4}
					/>
				</div>
				<div>
					<label className="block mb-2">Sitemap URL (optional)</label>
					<Input
						placeholder="https://example.com/sitemap.xml"
						value={sitemap}
						onChange={(e) => setSitemap(e.target.value)}
					/>
				</div>
				<Button 
					onClick={handleGenerate} 
					disabled={loading}
					className="w-full md:w-auto"
				>
					{loading ? (
						<>
							<Loader2 className="mr-2 h-4 w-4 animate-spin" />
							Generating...
						</>
					) : (
						"Generate Robots.txt"
					)}
				</Button>
				{result && (
					<div>
						<label className="block mb-2">Generated Robots.txt</label>
						<Textarea
							value={result}
							rows={12}
							readOnly
							className="font-mono"
						/>
					</div>
				)}
			</div>
		</div>
	);
}