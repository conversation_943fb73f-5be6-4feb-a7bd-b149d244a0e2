import { chats, messages as dbMessages } from "@/lib/db/schema";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { GoogleGenerativeAI } from "@google/generative-ai";
import { Message } from "ai";
import { eq } from "drizzle-orm";
import { nanoid } from "nanoid";

// Initialize Google AI
const genAI = new GoogleGenerativeAI(
  process.env.GOOGLE_GENERATIVE_AI_API_KEY || ""
);

export async function POST(req: Request) {
  try {
    const session = await auth();
    if (!session?.user) {
      return new Response("Unauthorized", { status: 401 });
    }

    const {
      messages: chatMessages,
      chatId: existingChatId,
      model: modelName = "gemini-pro",
    } = await req.json();
    console.log("Received request:", {
      messageCount: chatMessages.length,
      chatId: existingChatId,
      model: modelName,
      lastMessage: chatMessages[chatMessages.length - 1].content,
    });

    let chatId = existingChatId;
    let previousMessages: Message[] = [];

    try {
      // Create a new chat if one doesn't exist
      if (!chatId) {
        const result = await db
          .insert(chats)
          .values({
            id: nanoid(),
            userId: session.user.id,
            model: modelName,
            title: chatMessages[0].content.substring(0, 100) || "New Chat",
          })
          .returning();
        chatId = result[0].id;
        console.log("Created new chat:", { chatId });
      }

      // Get previous messages for context if chatId exists
      if (existingChatId) {
        const dbMsgs = await db
          .select()
          .from(dbMessages)
          .where(eq(dbMessages.chatId, existingChatId))
          .orderBy(dbMessages.createdAt);

        previousMessages = dbMsgs.map((msg) => ({
          id: msg.id,
          role: msg.role as Message["role"],
          content: msg.content,
        }));
        console.log("Retrieved previous messages:", {
          count: previousMessages.length,
        });
      }

      // Save user message
      const messageId = nanoid();
      await db.insert(dbMessages).values({
        id: messageId,
        chatId: chatId || nanoid(),
        role: "user",
        content: chatMessages[chatMessages.length - 1].content,
      });
      console.log("Saved user message:", { messageId });
    } catch (dbError) {
      console.warn(
        "[DB_WARNING] Database operations failed, continuing without persistence:",
        dbError
      );
    }

    // Format messages for the model
    const formattedMessages = [...previousMessages, ...chatMessages].map(
      (msg) => ({
        role: msg.role === "user" ? "user" : "model",
        parts: [{ text: msg.content }],
      })
    );

    // Initialize the model
    const geminiModel = genAI.getGenerativeModel({ model: modelName });

    // Start the chat
    const chat = geminiModel.startChat({
      history: formattedMessages.slice(0, -1),
      generationConfig: {
        maxOutputTokens: 10000,
      },
    });

    // Get the streaming response
    const result = await chat.sendMessageStream(
      formattedMessages[formattedMessages.length - 1].parts[0].text
    );

    const encoder = new TextEncoder();
    const responseId = nanoid();
    let fullText = "";

    const stream = new TransformStream({
      async transform(chunk, controller) {
        const text = chunk.text();
        fullText += text;

        // Send the message as a JSON string
        const message = {
          content: fullText,
        };
        controller.enqueue(
          encoder.encode(`data: ${JSON.stringify(message)}\n\n`)
        );
      },
      async flush(controller) {
        // Send the DONE message
        controller.enqueue(encoder.encode("data: [DONE]\n\n"));

        if (chatId) {
          try {
            await db.insert(dbMessages).values({
              id: responseId,
              chatId,
              role: "assistant",
              content: fullText,
            });
            console.log("Saved assistant response");
          } catch (dbError) {
            console.warn(
              "[DB_WARNING] Failed to save assistant response:",
              dbError
            );
          }
        }
      },
    });

    // Create a readable stream from the response
    const readable = new ReadableStream({
      async start(controller) {
        try {
          for await (const chunk of result.stream) {
            controller.enqueue(chunk);
          }
          controller.close();
        } catch (error) {
          controller.error(error);
        }
      },
    });

    readable.pipeThrough(stream);

    return new Response(stream.readable, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
        ...(chatId && { "X-Chat-Id": chatId }),
      },
    });
  } catch (error) {
    console.error("[GEMINI_ERROR]", {
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
    });
    return new Response("Internal Server Error", { status: 500 });
  }
}
