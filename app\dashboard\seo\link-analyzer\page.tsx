"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Loader2 } from "lucide-react";
import { Card } from "@/components/ui/card";

interface LinkAnalysis {
	url: string;
	status: number;
	isSecure: boolean;
	loadTime: number;
	seoIssues: string[];
	securityIssues: string[];
	redirectChain: string[];
}

export default function LinkAnalyzerPage() {
	const [url, setUrl] = useState("");
	const [loading, setLoading] = useState(false);
	const [result, setResult] = useState<LinkAnalysis | null>(null);
	const { toast } = useToast();

	const handleAnalyze = async () => {
		if (!url.trim()) {
			toast({
				title: "Error",
				description: "Please enter a URL to analyze",
				variant: "destructive",
			});
			return;
		}

		try {
			setLoading(true);
			const response = await fetch("/api/seo/link-analyzer", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ url }),
			});

			if (!response.ok) {
				throw new Error("Failed to analyze link");
			}

			const data = await response.json();
			setResult(data.result);
		} catch (error) {
			console.error("An error occurred:", error);
			toast({
				title: "Error",
				description: "Failed to analyze link. Please try again.",
				variant: "destructive",
			});
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="container mx-auto py-8">
			<h1 className="text-3xl font-bold mb-6">Link Analyzer</h1>
			<div className="grid gap-6">
				<div className="flex gap-4">
					<Input
						placeholder="Enter URL to analyze"
						value={url}
						onChange={(e) => setUrl(e.target.value)}
						className="flex-1"
					/>
					<Button 
						onClick={handleAnalyze} 
						disabled={loading}
					>
						{loading ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								Analyzing...
							</>
						) : (
							"Analyze Link"
						)}
					</Button>
				</div>
				
				{result && (
					<div className="space-y-6">
						<Card className="p-6">
							<div className="grid grid-cols-2 md:grid-cols-4 gap-6">
								<div>
									<h3 className="text-sm font-medium text-muted-foreground">Status</h3>
									<p className={`text-2xl font-bold ${result.status === 200 ? 'text-green-600' : 'text-red-600'}`}>
										{result.status}
									</p>
								</div>
								<div>
									<h3 className="text-sm font-medium text-muted-foreground">Security</h3>
									<p className={`text-2xl font-bold ${result.isSecure ? 'text-green-600' : 'text-yellow-600'}`}>
										{result.isSecure ? 'Secure' : 'Not Secure'}
									</p>
								</div>
								<div>
									<h3 className="text-sm font-medium text-muted-foreground">Load Time</h3>
									<p className="text-2xl font-bold">{result.loadTime}ms</p>
								</div>
							</div>
						</Card>

						{result.seoIssues.length > 0 && (
							<Card className="p-6">
								<h3 className="text-lg font-semibold mb-4">SEO Issues</h3>
								<ul className="list-disc pl-5 space-y-2">
									{result.seoIssues.map((issue, index) => (
										<li key={index} className="text-muted-foreground">{issue}</li>
									))}
								</ul>
							</Card>
						)}

						{result.securityIssues.length > 0 && (
							<Card className="p-6">
								<h3 className="text-lg font-semibold mb-4">Security Issues</h3>
								<ul className="list-disc pl-5 space-y-2">
									{result.securityIssues.map((issue, index) => (
										<li key={index} className="text-red-600">{issue}</li>
									))}
								</ul>
							</Card>
						)}

						{result.redirectChain.length > 0 && (
							<Card className="p-6">
								<h3 className="text-lg font-semibold mb-4">Redirect Chain</h3>
								<div className="space-y-2">
									{result.redirectChain.map((url, index) => (
										<div key={index} className="flex items-center">
											<span className="text-muted-foreground">{index + 1}.</span>
											<span className="ml-2">{url}</span>
										</div>
									))}
								</div>
							</Card>
						)}
					</div>
				)}
			</div>
		</div>
	);
}