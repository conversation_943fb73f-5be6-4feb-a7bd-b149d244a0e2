export class DeepSeekStreamParser {
	private buffer: string = '';
	private isInThinkingBlock: boolean = false;
	private isInCodeBlock: boolean = false;
	
	public parseStreamPart(chunk: string): string {
		this.buffer += chunk;
		
		// Handle thinking blocks
		if (this.buffer.includes('<think>')) {
			this.isInThinkingBlock = true;
			this.buffer = this.buffer.replace('<think>', '');
		}
		if (this.buffer.includes('</think>')) {
			this.isInThinkingBlock = false;
			this.buffer = this.buffer.replace('</think>', '');
		}
		
		// Handle code blocks
		if (this.buffer.includes('```')) {
			this.isInCodeBlock = !this.isInCodeBlock;
		}
		
		// Clean up any XML-like tags that might appear in the response
		const cleanedText = this.buffer.replace(/<[^>]*>/g, '');
		this.buffer = '';
		
		return cleanedText;
	}
	
	public isThinking(): boolean {
		return this.isInThinkingBlock;
	}
}

export function createDeepSeekStreamParser() {
	return new DeepSeekStreamParser();
}