import { GoogleGenerativeAI } from "@google/generative-ai";
import { NextResponse } from "next/server";

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);

export async function POST(req: Request) {
  try {
    const { domain } = await req.json();

    const model = genAI.getGenerativeModel({ model: "gemini-2.5-pro" });

    const prompt = `You are a WHOIS information provider. For the domain ${domain}, provide detailed WHOIS information in the following JSON format (respond with only the JSON, no markdown or code blocks):
    {
      "domain": "${domain}",
      "registrar": {
        "name": "Example Registrar",
        "url": "https://example.com",
        "whoisServer": "whois.example.com"
      },
      "registrant": {
        "organization": "Example Organization",
        "country": "US",
        "email": "<EMAIL>"
      },
      "dates": {
        "creationDate": "2024-01-01",
        "expiryDate": "2025-01-01",
        "updatedDate": "2024-03-15"
      },
      "nameservers": [
        "ns1.example.com",
        "ns2.example.com"
      ],
      "status": [
        "clientTransferProhibited",
        "clientUpdateProhibited"
      ]
    }`;

    const result = await model.generateContent(prompt);

    if (!result.response) {
      throw new Error("No response from AI");
    }

    const text = result.response.text();

    try {
      // Clean the response text to ensure it's valid JSON
      const cleanText = text.replace(/```json\n?|\n?```/g, "").trim();
      const jsonResult = JSON.parse(cleanText);

      // Ensure arrays are always arrays
      if (!Array.isArray(jsonResult.nameservers)) {
        jsonResult.nameservers = [jsonResult.nameservers].filter(Boolean);
      }
      if (!Array.isArray(jsonResult.status)) {
        jsonResult.status = [jsonResult.status].filter(Boolean);
      }

      return NextResponse.json({ result: jsonResult });
    } catch (parseError) {
      console.error("JSON parsing error:", parseError);
      // Fallback response
      return NextResponse.json({
        result: {
          domain: domain,
          registrar: {
            name: "Unknown Registrar",
            url: "",
            whoisServer: "",
          },
          registrant: {
            organization: "Private",
            country: "Unknown",
            email: null,
          },
          dates: {
            creationDate: "2024-01-01",
            expiryDate: "2025-01-01",
            updatedDate: "2024-03-15",
          },
          nameservers: ["ns1.example.com", "ns2.example.com"],
          status: ["unknown"],
        },
      });
    }
  } catch (error) {
    console.error("WHOIS checker error:", error);
    return NextResponse.json(
      { error: "Failed to fetch WHOIS information" },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({ error: "Method not allowed" }, { status: 405 });
}
