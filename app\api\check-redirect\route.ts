import { NextResponse } from "next/server";

// Define the interface for redirect results
interface RedirectResult {
	url: string;
	statusCode: number;
	redirectUrl?: string;
	error?: string;
}

async function followRedirect(url: string, maxRedirects = 5): Promise<RedirectResult[]> {
	const redirects: RedirectResult[] = [];
	let currentUrl = url;
	let redirectCount = 0;

	while (redirectCount < maxRedirects) {
		try {
			const response = await fetch(currentUrl, {
				method: "HEAD",
				redirect: "manual",
			});

			const result: RedirectResult = {
				url: currentUrl,
				statusCode: response.status,
			};

			redirects.push(result);

			if (response.status >= 300 && response.status < 400) {
				const location = response.headers.get("location");
				if (!location) break;

				// Handle relative URLs
				currentUrl = new URL(location, currentUrl).toString();
				redirectCount++;
				
				// Add the redirect URL to the previous result
				if (redirects.length > 0) {
					redirects[redirects.length - 1].redirectUrl = currentUrl;
				}
			} else {
				break;
			}
		} catch (error) {
			console.log(error);
			
			redirects.push({
				url: currentUrl,
				statusCode: 0,
				error: "Connection failed",
			});
			break;
		}
	}

	return redirects;
}

export async function POST(request: Request) {
	try {
		const { url } = await request.json();

		if (!url) {
			return NextResponse.json(
				{ error: "URL is required" },
				{ status: 400 }
			);
		}

		const redirects = await followRedirect(url);

		return NextResponse.json({ redirects });
	} catch (error) {
		console.log(error);
		return NextResponse.json(
			{ error: "Failed to check redirects" },
			{ status: 500 }
		);
	}
}