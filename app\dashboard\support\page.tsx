"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { ArrowRight, Clock, Mail, MessageSquare, Phone } from "lucide-react";

const supportChannels = [
  {
    title: "Live Chat",
    description: "Chat with our support team in real-time",
    icon: MessageSquare,
    action: "Start Chat",
    availability: "Online",
    responseTime: "< 5 mins"
  },
  {
    title: "Phone Support",
    description: "Get help over a phone call",
    icon: Phone,
    action: "Call Now",
    availability: "24/7",
    responseTime: "Immediate",
    badge: "Pro"
  },
  {
    title: "Email Support",
    description: "Send us your queries via email",
    icon: Mail,
    action: "Send Email",
    availability: "24/7",
    responseTime: "< 24 hours"
  }
];

const commonQuestions = [
  "How do I upgrade to Pro?",
  "Reset my password",
  "Billing issues",
  "API integration help",
  "Account settings",
  "Usage limits"
];

export default function SupportPage() {
  return (
    <div className="min-h-screen relative overflow-hidden py-6 sm:py-12">
      <div className="absolute inset-0 dashboard-gradient opacity-20" />
      <div className="relative px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
        <div className="flex flex-col gap-8">
          <div>
            <h1 className="text-4xl font-bold gradient-text mb-2">Support Center</h1>
            <p className="text-muted-foreground">
              Get help from our dedicated support team
            </p>
          </div>

          {/* Search Section */}
          <div className="glass-card p-8">
            <h2 className="text-2xl font-semibold mb-4">How can we help you?</h2>
            <div className="flex gap-4">
              <Input 
                placeholder="Search for help articles..." 
                className="flex-1 bg-white/5"
              />
              <Button>
                Search
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Support Channels */}
          <div className="grid gap-6 grid-cols-1 md:grid-cols-3">
            {supportChannels.map((channel) => (
              <div key={channel.title} className="glass-card p-6 h-full">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <channel.icon className="h-6 w-6 text-primary" />
                    <h2 className="text-xl font-semibold">{channel.title}</h2>
                  </div>
                  {channel.badge && (
                    <span className="text-xs font-medium bg-primary/20 text-primary px-2 py-0.5 rounded">
                      {channel.badge}
                    </span>
                  )}
                </div>
                <p className="text-muted-foreground mb-4">{channel.description}</p>
                <div className="flex items-center justify-between mb-4 text-sm">
                  <span className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    {channel.responseTime}
                  </span>
                  <span className={cn(
                    "px-2 py-0.5 rounded text-xs",
                    channel.availability === "Online" 
                      ? "bg-green-500/20 text-green-500"
                      : "bg-primary/20 text-primary"
                  )}>
                    {channel.availability}
                  </span>
                </div>
                <Button className="w-full">
                  {channel.action}
                </Button>
              </div>
            ))}
          </div>

          {/* Common Questions */}
          <div className="glass-card p-6">
            <h2 className="text-xl font-semibold mb-4">Common Questions</h2>
            <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
              {commonQuestions.map((question) => (
                <Button
                  key={question}
                  variant="outline"
                  className="justify-start hover:bg-white/5"
                >
                  <ArrowRight className="mr-2 h-4 w-4" />
                  {question}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
