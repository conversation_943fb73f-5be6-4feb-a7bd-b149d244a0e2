"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Loader2 } from "lucide-react";
import { Card } from "@/components/ui/card";

interface CPCResult {
	keyword: string;
	estimatedCPC: number;
	monthlySearches: number;
	competition: number;
	suggestedBid: number;
}

export default function CPCCalculatorPage() {
	const [keyword, setKeyword] = useState("");
	const [loading, setLoading] = useState(false);
	const [result, setResult] = useState<CPCResult | null>(null);
	const { toast } = useToast();

	const handleCalculate = async () => {
		if (!keyword.trim()) {
			toast({
				title: "Error",
				description: "Please enter a keyword",
				variant: "destructive",
			});
			return;
		}

		try {
			setLoading(true);
			const response = await fetch("/api/seo/cpc-calculator", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ keyword }),
			});

			if (!response.ok) {
				throw new Error("Failed to calculate CPC");
			}

			const data = await response.json();
			setResult(data.result);
		} catch (error) {
			console.error("An error occurred:", error);
			toast({
				title: "Error",
				description: "Failed to calculate CPC. Please try again.",
				variant: "destructive",
			});
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="container mx-auto py-8">
			<h1 className="text-3xl font-bold mb-6">Keyword CPC Calculator</h1>
			<div className="grid gap-6">
				<div className="flex gap-4">
					<Input
						placeholder="Enter keyword"
						value={keyword}
						onChange={(e) => setKeyword(e.target.value)}
						className="flex-1"
					/>
					<Button 
						onClick={handleCalculate} 
						disabled={loading}
					>
						{loading ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								Calculating...
							</>
						) : (
							"Calculate CPC"
						)}
					</Button>
				</div>
				
				{result && (
					<Card className="p-6">
						<div className="grid grid-cols-2 md:grid-cols-3 gap-6">
							<div>
								<h3 className="text-sm font-medium text-muted-foreground">Estimated CPC</h3>
								<p className="text-2xl font-bold">${result.estimatedCPC.toFixed(2)}</p>
							</div>
							<div>
								<h3 className="text-sm font-medium text-muted-foreground">Monthly Searches</h3>
								<p className="text-2xl font-bold">{result.monthlySearches.toLocaleString()}</p>
							</div>
							<div>
								<h3 className="text-sm font-medium text-muted-foreground">Competition</h3>
								<p className="text-2xl font-bold">{result.competition}%</p>
							</div>
							<div>
								<h3 className="text-sm font-medium text-muted-foreground">Suggested Bid</h3>
								<p className="text-2xl font-bold">${result.suggestedBid.toFixed(2)}</p>
							</div>
						</div>
					</Card>
				)}
			</div>
		</div>
	);
}