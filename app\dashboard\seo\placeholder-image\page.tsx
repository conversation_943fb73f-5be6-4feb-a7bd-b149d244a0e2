"use client";

import { useState } from "react";
import { ImageIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import Image from "next/image";

interface PlaceholderConfig {
	width: number;
	height: number;
	text: string;
	backgroundColor: string;
	textColor: string;
	format: "jpeg" | "png";
}

export default function PlaceholderImagePage() {
	const [config, setConfig] = useState<PlaceholderConfig>({
		width: 300,
		height: 200,
		text: "Placeholder",
		backgroundColor: "#cccccc",
		textColor: "#333333",
		format: "jpeg"
	});
	const [imageUrl, setImageUrl] = useState<string>("");

	const generateImage = async () => {
		try {
			const response = await fetch("/api/generate-placeholder", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify(config),
			});

			const data = await response.json();
			if (!response.ok) throw new Error(data.error);
			
			setImageUrl(data.imageUrl);
		} catch (error) {
			console.error("Failed to generate image:", error);
		}
	};

	return (
		<div className="container mx-auto py-8">
			<div className="flex flex-col gap-6">
				<div>
					<h1 className="text-4xl font-bold gradient-text mb-2">Image Placeholder Generator</h1>
					<p className="text-muted-foreground">
						Generate custom placeholder images for your projects
					</p>
				</div>

				<Card className="p-6">
					<div className="grid gap-6 md:grid-cols-2">
						<div className="flex flex-col gap-4">
							<div className="grid grid-cols-2 gap-4">
								<div>
									<Label>Width (px)</Label>
									<Input
										type="number"
										value={config.width}
										onChange={(e) => setConfig({ ...config, width: parseInt(e.target.value) })}
									/>
								</div>
								<div>
									<Label>Height (px)</Label>
									<Input
										type="number"
										value={config.height}
										onChange={(e) => setConfig({ ...config, height: parseInt(e.target.value) })}
									/>
								</div>
							</div>

							<div>
								<Label>Text</Label>
								<Input
									value={config.text}
									onChange={(e) => setConfig({ ...config, text: e.target.value })}
								/>
							</div>

							<div className="grid grid-cols-2 gap-4">
								<div>
									<Label>Background Color</Label>
									<Input
										type="color"
										value={config.backgroundColor}
										onChange={(e) => setConfig({ ...config, backgroundColor: e.target.value })}
									/>
								</div>
								<div>
									<Label>Text Color</Label>
									<Input
										type="color"
										value={config.textColor}
										onChange={(e) => setConfig({ ...config, textColor: e.target.value })}
									/>
								</div>
							</div>

							<div>
								<Label>Format</Label>
								<Select
									value={config.format}
									onValueChange={(value: "jpeg" | "png") => setConfig({ ...config, format: value })}
								>
									<SelectTrigger>
										<SelectValue />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="jpeg">JPEG</SelectItem>
										<SelectItem value="png">PNG</SelectItem>
									</SelectContent>
								</Select>
							</div>

							<Button onClick={generateImage} className="w-full">
								<ImageIcon className="mr-2 h-4 w-4" />
								Generate Image
							</Button>
						</div>

						<div className="flex flex-col gap-4">
							{imageUrl && (
								<div className="flex flex-col gap-2">
									<div className="relative w-full aspect-[3/2] border rounded-lg overflow-hidden">
										<Image
											src={imageUrl}
											alt="Generated Placeholder"
											width={config.width}
											height={config.height}
											className="object-contain"
											style={{
												maxWidth: '100%',
												height: 'auto'
											}}
										/>
									</div>
									<Input value={imageUrl} readOnly />
								</div>
							)}
						</div>
					</div>
				</Card>
			</div>
		</div>
	);
}