"use client";

import { useState } from "react";
import { FileOutput } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card } from "@/components/ui/card";
import { Label } from "@/components/ui/label";

interface PolicyFormData {
	companyName: string;
	website: string;
	email: string;
	collectsPersonalData: boolean;
	usesAnalytics: boolean;
	usesCookies: boolean;
}

export default function PrivacyPolicyPage() {
	const [formData, setFormData] = useState<PolicyFormData>({
		companyName: "",
		website: "",
		email: "",
		collectsPersonalData: false,
		usesAnalytics: false,
		usesCookies: false,
	});
	const [generatedPolicy, setGeneratedPolicy] = useState("");

	const handleSubmit = async () => {
		try {
			const response = await fetch("/api/generate-privacy-policy", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify(formData),
			});
			
			const data = await response.json();
			setGeneratedPolicy(data.policy);
		} catch (error) {
			console.error("Failed to generate policy:", error);
		}
	};

	return (
		<div className="container mx-auto py-8">
			<div className="flex flex-col gap-6">
				<div>
					<h1 className="text-4xl font-bold gradient-text mb-2">Privacy Policy Generator</h1>
					<p className="text-muted-foreground">
						Generate a custom privacy policy for your website
					</p>
				</div>

				<Card className="p-6">
					<div className="flex flex-col gap-4">
						<div>
							<Label>Company Name</Label>
							<Input
								value={formData.companyName}
								onChange={(e) => setFormData({ ...formData, companyName: e.target.value })}
								placeholder="Enter your company name"
							/>
						</div>

						<div>
							<Label>Website URL</Label>
							<Input
								value={formData.website}
								onChange={(e) => setFormData({ ...formData, website: e.target.value })}
								placeholder="https://example.com"
							/>
						</div>

						<div>
							<Label>Contact Email</Label>
							<Input
								value={formData.email}
								onChange={(e) => setFormData({ ...formData, email: e.target.value })}
								placeholder="<EMAIL>"
								type="email"
							/>
						</div>

						<div className="flex flex-col gap-2">
							<Label className="flex items-center gap-2">
								<input
									type="checkbox"
									checked={formData.collectsPersonalData}
									onChange={(e) => setFormData({ ...formData, collectsPersonalData: e.target.checked })}
								/>
								Collects Personal Data
							</Label>

							<Label className="flex items-center gap-2">
								<input
									type="checkbox"
									checked={formData.usesAnalytics}
									onChange={(e) => setFormData({ ...formData, usesAnalytics: e.target.checked })}
								/>
								Uses Analytics Tools
							</Label>

							<Label className="flex items-center gap-2">
								<input
									type="checkbox"
									checked={formData.usesCookies}
									onChange={(e) => setFormData({ ...formData, usesCookies: e.target.checked })}
								/>
								Uses Cookies
							</Label>
						</div>

						<Button onClick={handleSubmit} className="w-full">
							<FileOutput className="mr-2 h-4 w-4" />
							Generate Privacy Policy
						</Button>

						{generatedPolicy && (
							<div>
								<Label>Generated Privacy Policy</Label>
								<Textarea
									value={generatedPolicy}
									readOnly
									rows={20}
									className="mt-2"
								/>
							</div>
						)}
					</div>
				</Card>
			</div>
		</div>
	);
}