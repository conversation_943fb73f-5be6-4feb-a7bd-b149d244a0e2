import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { z } from "zod";

const creditActionSchema = z.object({
  action: z.enum(["set", "add", "subtract", "reset"]),
  amount: z.number().min(0).max(1000).optional(),
});

// Check admin authorization
async function checkAdminAuth() {
  const session = await auth();

  if (!session?.user?.id) {
    return { error: "Unauthorized - Please log in", status: 401 };
  }

  const user = await db.query.users.findFirst({
    where: (users, { eq }) => eq(users.id, session.user.id),
  });

  if (!user || user.userType !== "admin") {
    return { error: "Unauthorized - Admin access required", status: 403 };
  }

  return { user, session };
}

export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authResult = await checkAdminAuth();
    if ("error" in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    const resolvedParams = await params;
    const userId = resolvedParams.id;
    const body = await req.json();

    const validationResult = creditActionSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: "Validation failed",
          details: validationResult.error.issues,
        },
        { status: 400 }
      );
    }

    const { action, amount } = validationResult.data;

    // Get current user
    const targetUser = await db.query.users.findFirst({
      where: eq(users.id, userId),
      columns: {
        id: true,
        email: true,
        credits: true,
      },
    });

    if (!targetUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    let newCredits: number;
    let actionDescription: string;

    switch (action) {
      case "set":
        if (amount === undefined) {
          return NextResponse.json(
            { error: "Amount is required for set action" },
            { status: 400 }
          );
        }
        newCredits = amount;
        actionDescription = `Set credits to ${amount}`;
        break;

      case "add":
        if (amount === undefined) {
          return NextResponse.json(
            { error: "Amount is required for add action" },
            { status: 400 }
          );
        }
        newCredits = Math.min(targetUser.credits + amount, 1000);
        actionDescription = `Added ${amount} credits`;
        break;

      case "subtract":
        if (amount === undefined) {
          return NextResponse.json(
            { error: "Amount is required for subtract action" },
            { status: 400 }
          );
        }
        newCredits = Math.max(targetUser.credits - amount, 0);
        actionDescription = `Subtracted ${amount} credits`;
        break;

      case "reset":
        newCredits = 10; // Default daily credits
        actionDescription = "Reset credits to daily amount (10)";
        break;

      default:
        return NextResponse.json({ error: "Invalid action" }, { status: 400 });
    }

    // Update credits
    const updatedUser = await db
      .update(users)
      .set({
        credits: newCredits,
        lastCreditReset: action === "reset" ? new Date() : undefined,
        updatedAt: new Date(),
      })
      .where(eq(users.id, userId))
      .returning({
        id: users.id,
        email: users.email,
        credits: users.credits,
      });

    return NextResponse.json({
      success: true,
      message: `${actionDescription} for ${targetUser.email}`,
      data: {
        previousCredits: targetUser.credits,
        newCredits: updatedUser[0].credits,
        action,
        amount,
      },
    });
  } catch (error) {
    console.error("Failed to manage credits:", error);
    return NextResponse.json(
      {
        error: "Failed to manage credits",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// GET - Get user's current credits
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authResult = await checkAdminAuth();
    if ("error" in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    const resolvedParams = await params;
    const userId = resolvedParams.id;

    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
      columns: {
        id: true,
        email: true,
        name: true,
        credits: true,
        lastCreditReset: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: user,
    });
  } catch (error) {
    console.error("Failed to get user credits:", error);
    return NextResponse.json(
      {
        error: "Failed to get user credits",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
