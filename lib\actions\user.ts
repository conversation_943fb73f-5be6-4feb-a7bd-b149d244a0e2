"use server";

import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/auth.config";

export async function getAgencyUsers() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return {
        error: "Unauthorized",
      };
    }

    const userType = (session.user as { userType?: string })?.userType;

    if (userType !== "agency-basic" && userType !== "agency-deluxe") {
      return {
        error: "Not an agency user",
      };
    }

    const allUsers = await db.query.users.findMany({
      where: eq(users.createdBy, session.user.id),
    });

    return {
      success: true,
      data: allUsers,
    };
  } catch (error) {
    console.error("Error fetching users:", error);
    return {
      error: "Failed to fetch users",
    };
  }
}

export async function deleteAgencyUser(userId: string) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return {
        error: "Unauthorized",
      };
    }

    await db
      .delete(users)
      .where(and(eq(users.id, userId), eq(users.createdBy, session.user.id)));

    return {
      success: true,
      message: "User deleted successfully",
    };
  } catch (error) {
    console.error("Error deleting user:", error);
    return {
      error: "Failed to delete user",
    };
  }
}
