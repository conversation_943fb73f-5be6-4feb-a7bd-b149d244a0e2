"use client";

import { Sidebar } from "@/components/layout/sidebar";
import { ThemeToggle } from "@/components/theme-toggle";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Menu, X } from "lucide-react";
import { useState } from "react";

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  return (
    <div className="min-h-screen flex relative">
      {/* Unique Futuristic Background */}
      <div className="fixed inset-0 bg-gradient-to-br from-slate-950 via-purple-950 to-slate-950" />
      <div className="fixed inset-0 bg-gradient-to-tr from-cyan-950/30 via-transparent to-pink-950/30" />

      {/* Dynamic Grid Pattern */}
      <div
        className="fixed inset-0 opacity-20"
        style={{
          backgroundImage: `
          linear-gradient(rgba(6, 182, 212, 0.1) 1px, transparent 1px),
          linear-gradient(90deg, rgba(6, 182, 212, 0.1) 1px, transparent 1px)
        `,
          backgroundSize: "50px 50px",
        }}
      />

      {/* Animated Tech Elements */}
      <div className="fixed top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-cyan-400/50 to-transparent animate-pulse" />
      <div className="fixed bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-pink-400/50 to-transparent animate-pulse delay-1000" />

      {/* Floating Geometric Shapes */}
      <div
        className="fixed top-20 left-20 w-4 h-4 border-2 border-cyan-400/30 rotate-45 animate-spin"
        style={{ animationDuration: "20s" }}
      />
      <div className="fixed top-40 right-32 w-6 h-6 border-2 border-purple-400/30 animate-pulse" />
      <div className="fixed bottom-32 left-40 w-3 h-3 bg-pink-400/30 rounded-full animate-ping" />
      <div
        className="fixed bottom-20 right-20 w-5 h-5 border-2 border-cyan-400/30 rounded-full animate-bounce"
        style={{ animationDuration: "3s" }}
      />

      {/* Radial Gradients */}
      <div className="fixed top-1/4 left-1/4 w-96 h-96 bg-gradient-radial from-cyan-500/10 to-transparent rounded-full blur-3xl" />
      <div className="fixed bottom-1/4 right-1/4 w-80 h-80 bg-gradient-radial from-purple-500/10 to-transparent rounded-full blur-3xl" />

      {/* Mobile Menu Button */}
      <Button
        variant="ghost"
        size="icon"
        className="fixed top-4 left-4 z-50 md:hidden glass-card"
        onClick={() => setIsSidebarOpen(!isSidebarOpen)}
      >
        {isSidebarOpen ? (
          <X className="h-6 w-6" />
        ) : (
          <Menu className="h-6 w-6" />
        )}
      </Button>

      {/* Sidebar Container */}
      <div
        className={`fixed md:sticky top-0 h-screen w-64 transform ${
          isSidebarOpen ? "translate-x-0" : "-translate-x-full"
        } md:translate-x-0 transition duration-200 ease-in-out z-40`}
      >
        <div className="h-full">
          <Sidebar />
        </div>
      </div>

      {/* Backdrop */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-30 md:hidden"
          onClick={() => setIsSidebarOpen(false)}
        />
      )}

      {/* Main Content */}
      <div className="flex-1 min-w-0 relative z-10">
        <div className="fixed top-4 right-4 z-50">
          <ThemeToggle />
        </div>
        <main className="relative z-10">{children}</main>
      </div>
    </div>
  );
}
