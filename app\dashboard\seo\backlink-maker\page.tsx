"use client";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Loader2 } from "lucide-react";
import { useState } from "react";
export default function BacklinkMakerPage() {
	const [url, setUrl] = useState("");
	const [keywords, setKeywords] = useState("");
	const [loading, setLoading] = useState(false);
	const [result, setResult] = useState<Array<{
		platform: string;
		link: string;
		instructions: string;
	}> | null>(null);
	const { toast } = useToast();

	const handleGenerate = async () => {
		if (!url.trim() || !keywords.trim()) {
			toast({
				title: "Error",
				description: "Please fill in all fields",
				variant: "destructive",
			});
			return;
		}

		try {
			setLoading(true);
			const response = await fetch("/api/seo/backlink-maker", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ url, keywords }),
			});

			if (!response.ok) {
				throw new Error("Failed to generate backlinks");
			}

			const data = await response.json();
			setResult(data.result);
		} catch (error) {
            console.error("An error occurred:", error);
			toast({
				title: "Error",
				description: "Failed to generate backlinks. Please try again.",
				variant: "destructive",
			});
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="container mx-auto py-8">
			<h1 className="text-3xl font-bold mb-6">Backlink Maker</h1>
			<div className="grid gap-6">
				<div>
					<label className="block mb-2">Website URL</label>
					<Input
						placeholder="Enter your website URL"
						value={url}
						onChange={(e) => setUrl(e.target.value)}
					/>
				</div>
				<div>
					<label className="block mb-2">Target Keywords</label>
					<Input
						placeholder="Enter target keywords (comma-separated)"
						value={keywords}
						onChange={(e) => setKeywords(e.target.value)}
					/>
				</div>
				<Button 
					onClick={handleGenerate} 
					disabled={loading}
					className="w-full md:w-auto"
				>
					{loading ? (
						<>
							<Loader2 className="mr-2 h-4 w-4 animate-spin" />
							Generating...
						</>
					) : (
						"Generate Backlinks"
					)}
				</Button>
				{result && (
					<div className="space-y-4">
						<h2 className="text-xl font-semibold">Backlink Opportunities</h2>
						{result.map((item, index) => (
							<div key={index} className="p-4 border rounded-lg">
								<h3 className="font-medium mb-2">{item.platform}</h3>
								<p className="mb-2">
									<strong>Link:</strong>{" "}
									<a 
										href={item.link} 
										target="_blank" 
										rel="noopener noreferrer"
										className="text-primary hover:underline"
									>
										{item.link}
									</a>
								</p>
								<p className="text-sm text-muted-foreground">
									{item.instructions}
								</p>
							</div>
						))}
					</div>
				)}
			</div>
		</div>
	);
}