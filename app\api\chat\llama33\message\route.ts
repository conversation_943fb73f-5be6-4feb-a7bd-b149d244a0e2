import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { chats, messages } from "@/lib/db/schema";
import { nanoid } from "nanoid";
import Together from "together-ai";
import { eq } from "drizzle-orm";

interface ChatMessage {
  role: "user" | "assistant";
  content: string;
}

const together = new Together({
  apiKey: process.env.TOGETHER_API_KEY,
});

export async function POST(req: Request) {
  try {
    const session = await auth();
    if (!session?.user) {
      return new Response(JSON.stringify({ error: "Unauthorized" }), {
        status: 401,
      });
    }

    const { messages: chatMessages, chatId } = await req.json();

    // Validate chatId
    if (!chatId) {
      return new Response(JSON.stringify({ error: "Chat ID is required" }), {
        status: 400,
      });
    }

    // Check if chat exists and create if needed
    let existingChat = await db.query.chats.findFirst({
      where: eq(chats.id, chatId),
    });

    if (!existingChat) {
      const [newChat] = await db
        .insert(chats)
        .values({
          id: chatId,
          userId: session.user.id,
          model: "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
          title: chatMessages[0]?.content.substring(0, 100) || "New Chat",
          createdAt: new Date(),
        })
        .returning();
      existingChat = newChat;
    }

    const messageId = nanoid();
    const userMessageId = nanoid();

    // Store user message
    await db.insert(messages).values({
      id: userMessageId,
      chatId,
      role: "user",
      content: chatMessages[chatMessages.length - 1].content,
      createdAt: new Date(),
    });

    // Create initial assistant message
    await db.insert(messages).values({
      id: messageId,
      chatId,
      role: "assistant",
      content: "",
      createdAt: new Date(),
    });

    const completion = await together.chat.completions.create({
      messages: chatMessages.map((msg: ChatMessage) => ({
        role: msg.role,
        content: msg.content,
      })),
      model: "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
      max_tokens: 8000,
      temperature: 0.7,
      top_p: 0.7,
      top_k: 50,
      repetition_penalty: 1,
      stop: ["<|end▁of▁sentence|>"],
      stream: true,
    });

    const CHUNK_SIZE = 1000;
    let fullResponse = "";
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
      async start(controller) {
        let keepAliveInterval: NodeJS.Timeout | undefined = undefined;

        try {
          // Setup keep-alive ping
          keepAliveInterval = setInterval(() => {
            try {
              controller.enqueue(encoder.encode(": ping\n\n"));
            } catch (error) {
              console.error("Keep-alive error:", error);
              clearInterval(keepAliveInterval);
            }
          }, 15000);

          for await (const chunk of completion) {
            const text = chunk.choices[0]?.delta?.content || "";
            if (text) {
              fullResponse += text;

              // Split large text into smaller chunks
              const chunks = text.match(
                new RegExp(`.{1,${CHUNK_SIZE}}`, "g")
              ) || [text];
              for (const subChunk of chunks) {
                const chunkData = {
                  id: messageId,
                  role: "assistant",
                  content: subChunk,
                };

                try {
                  controller.enqueue(
                    encoder.encode(`data: ${JSON.stringify(chunkData)}\n\n`)
                  );
                  await new Promise((resolve) => setTimeout(resolve, 50));
                  // eslint-disable-next-line @typescript-eslint/no-explicit-any
                } catch (error: any) {
                  if (error?.message?.includes("QUOTA_BYTES")) {
                    console.warn(
                      "QUOTA_BYTES exceeded, waiting before next chunk"
                    );
                    await new Promise((resolve) => setTimeout(resolve, 100));
                    continue;
                  }
                  throw error;
                }
              }
            }
          }

          // Update final content in database
          await db
            .update(messages)
            .set({ content: fullResponse })
            .where(eq(messages.id, messageId));

          controller.enqueue(encoder.encode("data: [DONE]\n\n"));
        } catch (error) {
          console.error("Stream processing error:", error);
          controller.error(error);
        } finally {
          if (keepAliveInterval) {
            clearInterval(keepAliveInterval);
          }
          controller.close();
        }
      },
    });

    return new Response(stream, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache, no-transform",
        Connection: "keep-alive",
        "X-Accel-Buffering": "no",
        "Transfer-Encoding": "chunked",
        ...(chatId && { "X-Chat-Id": chatId }),
      },
    });
  } catch (error) {
    console.error("[LLAMA33_CHAT_ERROR]", error);
    return new Response(
      JSON.stringify({
        error: error instanceof Error ? error.message : "Internal server error",
      }),
      { status: 500 }
    );
  }
}
