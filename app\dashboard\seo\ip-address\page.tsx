"use client";

import { useState, useEffect, useCallback } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Loader2, Copy } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface IPInfo {
  ip: string;
  country: string;
  region: string;
  city: string;
  isp: string;
  timezone: string;
  latitude: number;
  longitude: number;
}

export default function IPAddressPage() {
  const [loading, setLoading] = useState(true);
  const [ipInfo, setIpInfo] = useState<IPInfo | null>(null);
  const { toast } = useToast();

  const fetchIPInfo = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/seo/ip-address");
      if (!response.ok) throw new Error("Failed to fetch IP info");
      const data = await response.json();
      setIpInfo(data.result);
    } catch (error) {
      console.error("An error occurred:", error);
      toast({
        title: "Error",
        description: "Failed to fetch IP information",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    fetchIPInfo();
  }, [fetchIPInfo]);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied",
      description: "IP address copied to clipboard",
    });
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">My IP Address</h1>

      {loading ? (
        <div className="flex items-center justify-center p-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : ipInfo ? (
        <div className="grid gap-6">
          <Card className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-2xl font-bold">{ipInfo.ip}</h2>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => copyToClipboard(ipInfo.ip)}
              >
                <Copy className="h-4 w-4" />
              </Button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">Location</p>
                <p>{`${ipInfo.city}, ${ipInfo.region}, ${ipInfo.country}`}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">ISP</p>
                <p>{ipInfo.isp}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Timezone</p>
                <p>{ipInfo.timezone}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Coordinates</p>
                <p>{`${ipInfo.latitude}, ${ipInfo.longitude}`}</p>
              </div>
            </div>
          </Card>

          <Button onClick={fetchIPInfo} className="w-full md:w-auto">
            Refresh IP Info
          </Button>
        </div>
      ) : (
        <p>No IP information available</p>
      )}
    </div>
  );
}
