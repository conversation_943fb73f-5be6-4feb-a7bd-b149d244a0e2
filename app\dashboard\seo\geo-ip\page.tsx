"use client";

import { useState } from "react";
import { Map } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface GeoIPResult {
	ip: string;
	country: string;
	city: string;
	latitude: number;
	longitude: number;
	error?: string;
}

export default function GeoIPPage() {
	const [ips, setIps] = useState("");
	const [loading, setLoading] = useState(false);
	const [results, setResults] = useState<GeoIPResult[]>([]);
	const [error, setError] = useState("");

	const locateIPs = async () => {
		setLoading(true);
		setError("");
		try {
			const ipList = ips.split("\n").map(ip => ip.trim()).filter(Boolean);
			const response = await fetch("/api/geo-ip", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({ ips: ipList }),
			});
			
			const data = await response.json();
			if (!response.ok) throw new Error(data.error);
			setResults(data.results);
		} catch (err) {
			setError(err instanceof Error ? err.message : "Failed to locate IPs");
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="container mx-auto py-8">
			<div className="flex flex-col gap-6">
				<div>
					<h1 className="text-4xl font-bold gradient-text mb-2">Bulk GEO IP Locator</h1>
					<p className="text-muted-foreground">
						Locate multiple IP addresses geographically
					</p>
				</div>

				<Card className="p-6">
					<div className="flex flex-col gap-4">
						<div>
							<label className="block text-sm font-medium mb-2">
								IP Addresses (one per line)
							</label>
							<Textarea
								placeholder="Enter IP addresses..."
								value={ips}
								onChange={(e) => setIps(e.target.value)}
								rows={5}
							/>
						</div>

						<Button onClick={locateIPs} disabled={loading}>
							<Map className="mr-2 h-4 w-4" />
							{loading ? "Locating..." : "Locate IPs"}
						</Button>

						{error && (
							<Alert variant="destructive">
								<AlertDescription>{error}</AlertDescription>
							</Alert>
						)}

						{results.length > 0 && (
							<div className="grid gap-4">
								{results.map((result, index) => (
									<Card key={index} className="p-4">
										<div className="grid grid-cols-2 gap-4">
											<div>
												<p className="font-medium">IP Address</p>
												<p className="text-muted-foreground">{result.ip}</p>
											</div>
											<div>
												<p className="font-medium">Country</p>
												<p className="text-muted-foreground">{result.country}</p>
											</div>
											<div>
												<p className="font-medium">City</p>
												<p className="text-muted-foreground">{result.city}</p>
											</div>
											<div>
												<p className="font-medium">Coordinates</p>
												<p className="text-muted-foreground">
													{result.latitude}, {result.longitude}
												</p>
											</div>
										</div>
									</Card>
								))}
							</div>
						)}
					</div>
				</Card>
			</div>
		</div>
	);
}