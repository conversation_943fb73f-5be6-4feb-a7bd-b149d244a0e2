"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Link as LinkIcon, X } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { shareWithUsers } from "@/lib/actions/admin";
type ShareContent = {
	title: string;
	description: string;
	link?: string;
	type: 'demo' | 'issue' | 'announcement';
	targetEmails: string[];
};

export default function SharePage() {
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [emailInput, setEmailInput] = useState("");
	const [content, setContent] = useState<ShareContent>({
		title: '',
		description: '',
		link: '',
		type: 'demo',
		targetEmails: []
	});
	const { toast } = useToast();

	const isValidEmail = (email: string) => {
		return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
	};

	const handleAddEmail = () => {
		if (!emailInput.trim()) return;
		if (!isValidEmail(emailInput)) {
			toast({
				title: "Invalid Email",
				description: "Please enter a valid email address",
				variant: "destructive",
			});
			return;
		}
		if (!content.targetEmails.includes(emailInput)) {
			setContent(prev => ({
				...prev,
				targetEmails: [...prev.targetEmails, emailInput.trim()]
			}));
		}
		setEmailInput("");
	};

	const handleRemoveEmail = (emailToRemove: string) => {
		setContent(prev => ({
			...prev,
			targetEmails: prev.targetEmails.filter(email => email !== emailToRemove)
		}));
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setIsSubmitting(true);

		try {
			const result = await shareWithUsers(content);

			if (!result.success) {
				throw new Error(result.error);
			}


			toast({
				title: "Success",
				description: content.targetEmails.length > 0 
					? `Content shared with ${content.targetEmails.length} specific users`
					: "Content shared with all users",
			});

			// Reset form
			setContent({
				title: '',
				description: '',
				link: '',
				type: 'demo',
				targetEmails: []
			});
		} catch (error) {
			console.error(error);
			toast({
				title: "Error",
				description: error instanceof Error ? error.message : "Failed to share content. Please try again.",
				variant: "destructive",
			});
		} finally {
			setIsSubmitting(false);
		}
	};

	return (
		<div className="p-6 max-w-2xl mx-auto">
			<h1 className="text-2xl font-bold mb-6">Share with Users</h1>
			
			<form onSubmit={handleSubmit} className="space-y-6">
				<div className="space-y-2">
					<label className="text-sm font-medium">Content Type</label>
					<select
						className="w-full p-2 rounded-md border border-input bg-background"
						value={content.type}
						onChange={(e) => setContent({ ...content, type: e.target.value as ShareContent['type'] })}
					>
						<option value="demo">Demo</option>
						<option value="issue">Issue Update</option>
						<option value="announcement">Announcement</option>
					</select>
				</div>

				<div className="space-y-2">
					<label className="text-sm font-medium">Title</label>
					<Input
						value={content.title}
						onChange={(e) => setContent({ ...content, title: e.target.value })}
						placeholder="Enter title"
						required
					/>
				</div>

				<div className="space-y-2">
					<label className="text-sm font-medium">Description</label>
					<Textarea
						value={content.description}
						onChange={(e) => setContent({ ...content, description: e.target.value })}
						placeholder="Enter description"
						required
						className="min-h-[100px]"
					/>
				</div>

				<div className="space-y-2">
					<label className="text-sm font-medium">Link (Optional)</label>
					<div className="relative">
						<LinkIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
						<Input
							value={content.link}
							onChange={(e) => setContent({ ...content, link: e.target.value })}
							placeholder="Enter relevant link"
							className="pl-10"
						/>
					</div>
				</div>

				<div className="space-y-2">
					<label className="text-sm font-medium">Target Specific Users (Optional)</label>
					<div className="flex gap-2">
						<Input
							type="email"
							value={emailInput}
							onChange={(e) => setEmailInput(e.target.value)}
							onKeyDown={(e) => {
								if (e.key === 'Enter') {
									e.preventDefault();
									handleAddEmail();
								}
							}}
							placeholder="Enter email address"
						/>
						<Button 
							type="button" 
							variant="secondary"
							onClick={handleAddEmail}
						>
							Add
						</Button>
					</div>
					<p className="text-sm text-muted-foreground">
						Leave empty to share with all users
					</p>
					{content.targetEmails.length > 0 && (
						<div className="flex flex-wrap gap-2 mt-2">
							{content.targetEmails.map(email => (
								<Badge key={email} variant="secondary" className="flex items-center gap-1">
									{email}
									<button
										type="button"
										onClick={() => handleRemoveEmail(email)}
										className="ml-1 hover:text-destructive"
									>
										<X className="h-3 w-3" />
									</button>
								</Badge>
							))}
						</div>
					)}
				</div>

				<Button
					type="submit"
					className="w-full"
					disabled={isSubmitting}
				>
					{isSubmitting ? (
						<>
							<Loader2 className="mr-2 h-4 w-4 animate-spin" />
							Sharing...
						</>
					) : (
						`Share with ${content.targetEmails.length > 0 ? 'Selected' : 'All'} Users`
					)}
				</Button>
			</form>
		</div>
	);
}