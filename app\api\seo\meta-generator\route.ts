import { GoogleGenerativeAI } from "@google/generative-ai";
import { NextResponse } from "next/server";

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);

export async function POST(req: Request) {
	try {
		const { url, title, description } = await req.json();
		
		const model = genAI.getGenerativeModel({ model: "gemini-pro" });
		
		const prompt = `Generate optimized meta tags for SEO based on the following information:

URL: ${url}
Title: ${title}
Description: ${description}

Please provide the following meta tags in HTML format:
1. Title tag
2. Meta description
3. Open Graph tags (og:title, og:description, og:url)
4. Twitter Card tags
5. Canonical URL

Make sure the meta tags are optimized for SEO best practices.`;

		const result = await model.generateContent(prompt);
		const response = await result.response;
		const text = response.text();

		return NextResponse.json({ result: text });
	} catch (error) {
		console.error("Meta tag generator error:", error);
		return NextResponse.json(
			{ error: "Failed to generate meta tags" },
			{ status: 500 }
		);
	}
}