"use server";

import { users } from "@/lib/db/schema";
import { db } from "@/lib/db";
import { hash } from "bcryptjs";
import { eq } from "drizzle-orm";
import { z } from "zod";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/auth.config";

const signUpSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
  name: z.string().min(2).max(50),
  userType: z.enum([
    "basic",
    "unlimited-lite",
    "unlimited-premium",
    "agency-basic",
    "agency-deluxe",
    "admin",
  ]),
  isAgencyUser: z.boolean().optional(),
});

export type SignUpData = z.infer<typeof signUpSchema>;

export async function signUp(formData: FormData) {
  const data = {
    email: formData.get("email"),
    password: formData.get("password"),
    name: formData.get("name"),
    userType: formData.get("userType"),
    isAgencyUser: formData.get("isAgencyUser") === "true",
  };

  const result = signUpSchema.safeParse(data);
  if (!result.success) {
    console.error("Validation error:", result.error);
    return {
      error: result.error.errors[0].message,
    };
  }

  const validatedData = result.data;

  try {
    const existingUser = await db.query.users.findFirst({
      where: eq(users.email, validatedData.email),
    });

    if (existingUser) {
      return {
        error: "Email already exists",
      };
    }

    const hashedPassword = await hash(validatedData.password, 10);
    const userId = crypto.randomUUID();

    // If it's an agency user creation, get the agency's session
    let createdById = null;
    if (validatedData.isAgencyUser) {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return {
          error: "Unauthorized agency user creation",
        };
      }
      createdById = session.user.id;
    }

    const userData = {
      id: userId,
      name: validatedData.name,
      email: validatedData.email,
      password: hashedPassword,
      userType: validatedData.userType,
      ...(createdById && { createdBy: createdById }),
    };

    await db.insert(users).values(userData);

    return {
      success: true,
      data: {
        id: userId,
        email: validatedData.email,
        name: validatedData.name,
        userType: validatedData.userType,
        createdBy: createdById,
      },
    };
  } catch (error) {
    console.error("Signup error:", error);
    return {
      error: "Something went wrong during signup",
    };
  }
}

export async function deleteUser(userId: string) {
  try {
    const existingUser = await db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (!existingUser) {
      return {
        error: "User not found",
      };
    }

    await db.delete(users).where(eq(users.id, userId));

    return {
      success: true,
      message: "User deleted successfully",
    };
  } catch (error) {
    console.error("Delete user error:", error);
    return {
      error: "Failed to delete user",
    };
  }
}

const loginSchema = z.object({
  email: z.string().email(),
  password: z.string(),
});

export type LoginData = z.infer<typeof loginSchema>;

export async function login(formData: FormData) {
  const data = {
    email: formData.get("email"),
    password: formData.get("password"),
  };

  const result = loginSchema.safeParse(data);
  if (!result.success) {
    return {
      error: result.error.errors[0].message,
    };
  }

  const validatedData = result.data;

  try {
    const user = await db.query.users.findFirst({
      where: eq(users.email, validatedData.email),
    });

    if (!user) {
      return {
        error: "Invalid credentials",
      };
    }

    return {
      success: true,
      data: {
        email: validatedData.email,
        password: validatedData.password,
      },
    };
  } catch (error) {
    console.error("Login error:", error);
    return {
      error: "Something went wrong during login",
    };
  }
}
