import { createStreamParser } from './stream-parser';

export async function processStream(
	stream: ReadableStream<Uint8Array>,
	onChunk?: (chunk: string) => void
): Promise<string> {
	const parser = createStreamParser();
	const reader = stream.getReader();
	let result = '';

	try {
		while (true) {
			const { done, value } = await reader.read();
			
			if (done) {
				const remaining = parser.flush();
				if (remaining) {
					result += remaining;
					onChunk?.(remaining);
				}
				break;
			}

			const parsed = parser.parseChunk(value);
			if (parsed) {
				result += parsed;
				onChunk?.(parsed);
			}

			// Add a small delay to prevent quota issues
			await new Promise(resolve => setTimeout(resolve, 10));
		}

		return result;
	} catch (error) {
		console.error('Stream processing error:', error);
		throw new Error('Failed to process stream: ' + (error as Error).message);
	} finally {
		reader.releaseLock();
	}
}