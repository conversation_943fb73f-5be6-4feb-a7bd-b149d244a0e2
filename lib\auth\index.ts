import NextAuth from "next-auth";
import { getServerSession } from "next-auth";
import { authConfig } from "./auth.config";
import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { hash } from "bcryptjs";
import crypto from "crypto";

interface SignUpData {
  email: string;
  password: string;
  name: string;
  userType:
    | "basic"
    | "unlimited-lite"
    | "unlimited-premium"
    | "agency-basic"
    | "agency-deluxe"
    | "admin";
}

export const auth = () => getServerSession(authConfig);

// Create a single instance of NextAuth
const handler = NextAuth(authConfig);
export const signIn = handler.signIn;
export const signOut = handler.signOut;

export async function signUpWithCredentials({
  email,
  password,
  name,
  userType,
}: SignUpData) {
  try {
    const existingUser = await db.query.users.findFirst({
      where: eq(users.email, email),
    });

    if (existingUser) {
      return {
        ok: false,
        error: "Email already exists",
      };
    }

    const hashedPassword = await hash(password, 10);

    await db.insert(users).values({
      id: crypto.randomUUID(),
      name,
      email,
      password: hashedPassword,
      userType,
    });

    return {
      ok: true,
    };
  } catch {
    return {
      ok: false,
      error: "Something went wrong",
    };
  }
}
