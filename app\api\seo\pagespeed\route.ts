import { GoogleGenerativeAI } from "@google/generative-ai";
import { NextResponse } from "next/server";

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);

export async function POST(req: Request) {
	try {
		const { url } = await req.json();
		
		const model = genAI.getGenerativeModel({ model: "gemini-pro" });
		
		const prompt = `Analyze the following URL for page speed insights and provide a detailed analysis. Return the response in this exact JSON format:
		{
			"performance": number (0-100),
			"accessibility": number (0-100),
			"bestPractices": number (0-100),
			"seo": number (0-100),
			"metrics": {
				"FCP": number (seconds),
				"LCP": number (seconds),
				"CLS": number,
				"TTI": number (seconds)
			},
			"suggestions": [
				{
					"category": "string",
					"impact": "high" | "medium" | "low",
					"description": "string"
				}
			]
		}

		URL: ${url}

		Provide realistic performance metrics and actionable suggestions.`;

		const result = await model.generateContent(prompt);
		const response = await result.response;
		const text = response.text();
		
		// Parse the response as JSON
		const jsonResult = JSON.parse(text.replace(/```json\n|```/g, '').trim());

		return NextResponse.json({ result: jsonResult });
	} catch (error) {
		console.error("PageSpeed analysis error:", error);
		return NextResponse.json(
			{ error: "Failed to analyze page speed" },
			{ status: 500 }
		);
	}
}