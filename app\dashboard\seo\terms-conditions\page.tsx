"use client";

import { useState } from "react";
import { Scale } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card } from "@/components/ui/card";
import { Label } from "@/components/ui/label";

interface TermsFormData {
	companyName: string;
	website: string;
	email: string;
	hasUserAccounts: boolean;
	hasPayments: boolean;
	hasUserContent: boolean;
}

export default function TermsConditionsPage() {
	const [formData, setFormData] = useState<TermsFormData>({
		companyName: "",
		website: "",
		email: "",
		hasUserAccounts: false,
		hasPayments: false,
		hasUserContent: false,
	});
	const [generatedTerms, setGeneratedTerms] = useState("");

	const handleSubmit = async () => {
		try {
			const response = await fetch("/api/generate-terms", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify(formData),
			});
			
			const data = await response.json();
			setGeneratedTerms(data.terms);
		} catch (error) {
			console.error("Failed to generate terms:", error);
		}
	};

	return (
		<div className="container mx-auto py-8">
			<div className="flex flex-col gap-6">
				<div>
					<h1 className="text-4xl font-bold gradient-text mb-2">Terms & Conditions Generator</h1>
					<p className="text-muted-foreground">
						Generate custom terms and conditions for your website
					</p>
				</div>

				<Card className="p-6">
					<div className="flex flex-col gap-4">
						<div>
							<Label>Company Name</Label>
							<Input
								value={formData.companyName}
								onChange={(e) => setFormData({ ...formData, companyName: e.target.value })}
								placeholder="Enter your company name"
							/>
						</div>

						<div>
							<Label>Website URL</Label>
							<Input
								value={formData.website}
								onChange={(e) => setFormData({ ...formData, website: e.target.value })}
								placeholder="https://example.com"
							/>
						</div>

						<div>
							<Label>Contact Email</Label>
							<Input
								value={formData.email}
								onChange={(e) => setFormData({ ...formData, email: e.target.value })}
								placeholder="<EMAIL>"
								type="email"
							/>
						</div>

						<div className="flex flex-col gap-2">
							<Label className="flex items-center gap-2">
								<input
									type="checkbox"
									checked={formData.hasUserAccounts}
									onChange={(e) => setFormData({ ...formData, hasUserAccounts: e.target.checked })}
								/>
								Has User Accounts
							</Label>

							<Label className="flex items-center gap-2">
								<input
									type="checkbox"
									checked={formData.hasPayments}
									onChange={(e) => setFormData({ ...formData, hasPayments: e.target.checked })}
								/>
								Includes Payments
							</Label>

							<Label className="flex items-center gap-2">
								<input
									type="checkbox"
									checked={formData.hasUserContent}
									onChange={(e) => setFormData({ ...formData, hasUserContent: e.target.checked })}
								/>
								Users Can Submit Content
							</Label>
						</div>

						<Button onClick={handleSubmit} className="w-full">
							<Scale className="mr-2 h-4 w-4" />
							Generate Terms & Conditions
						</Button>

						{generatedTerms && (
							<div>
								<Label>Generated Terms & Conditions</Label>
								<Textarea
									value={generatedTerms}
									readOnly
									rows={20}
									className="mt-2"
								/>
							</div>
						)}
					</div>
				</Card>
			</div>
		</div>
	);
}