"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import {
  Clock,
  Users,
  Settings,
  Play,
  Square,
  CheckCircle,
  XCircle,
  AlertCircle,
} from "lucide-react";

interface CreditReset {
  id: string;
  resetDate: string;
  usersAffected: number;
  creditsAssigned: number;
  status: "pending" | "completed" | "failed";
  createdAt: string;
  completedAt?: string;
  errorMessage?: string;
}

export function CreditManagement() {
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("");
  const [error, setError] = useState("");
  const [cronStatus, setCronStatus] = useState<{
    pgCronAvailable: boolean;
    cronJobs: Array<{
      jobname: string;
      schedule: string;
      command: string;
      active: boolean;
    }>;
    requestedBy: string;
  } | null>(null);
  const [recentResets, setRecentResets] = useState<CreditReset[]>([]);

  const checkCronStatus = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/admin/cron-setup");
      const data = await response.json();

      if (data.success) {
        setCronStatus(data);
        setMessage("Cron status updated");
      } else {
        setError(data.error || "Failed to check cron status");
      }
    } catch {
      setError("Failed to check cron status");
    } finally {
      setLoading(false);
    }
  };

  const setupCron = async () => {
    try {
      setLoading(true);
      setError("");
      const response = await fetch("/api/admin/cron-setup", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ action: "setup" }),
      });

      const data = await response.json();

      if (data.success) {
        setMessage(data.message);
        await checkCronStatus();
      } else {
        setError(data.error || "Failed to setup cron job");
      }
    } catch {
      setError("Failed to setup cron job");
    } finally {
      setLoading(false);
    }
  };

  const removeCron = async () => {
    try {
      setLoading(true);
      setError("");
      const response = await fetch("/api/admin/cron-setup", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ action: "remove" }),
      });

      const data = await response.json();

      if (data.success) {
        setMessage(data.message);
        await checkCronStatus();
      } else {
        setError(data.error || "Failed to remove cron job");
      }
    } catch {
      setError("Failed to remove cron job");
    } finally {
      setLoading(false);
    }
  };

  const triggerManualReset = async () => {
    try {
      setLoading(true);
      setError("");
      const response = await fetch("/api/admin/reset-daily-credits", {
        method: "POST",
      });

      const data = await response.json();

      if (data.success) {
        setMessage(
          `Manual reset completed! ${data.usersAffected} users affected.`
        );
        await fetchRecentResets();
      } else {
        setError(data.error || "Failed to trigger manual reset");
      }
    } catch {
      setError("Failed to trigger manual reset");
    } finally {
      setLoading(false);
    }
  };

  const fetchRecentResets = async () => {
    try {
      const response = await fetch("/api/admin/reset-daily-credits");
      const data = await response.json();

      if (data.success) {
        setRecentResets(data.recentResets || []);
      }
    } catch (err) {
      console.error("Failed to fetch recent resets:", err);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "failed":
        return <XCircle className="h-4 w-4 text-red-500" />;
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      completed: "default",
      failed: "destructive",
      pending: "secondary",
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || "outline"}>
        {status}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Daily Credit Management</h2>
        <Button onClick={checkCronStatus} disabled={loading}>
          <Settings className="h-4 w-4 mr-2" />
          Check Status
        </Button>
      </div>

      {message && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>{message}</AlertDescription>
        </Alert>
      )}

      {error && (
        <Alert variant="destructive">
          <XCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="grid gap-6 md:grid-cols-2">
        {/* Cron Job Status */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Cron Job Status</h3>

          {cronStatus ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span>pg_cron Available:</span>
                <Badge
                  variant={
                    cronStatus.pgCronAvailable ? "default" : "destructive"
                  }
                >
                  {cronStatus.pgCronAvailable ? "Yes" : "No"}
                </Badge>
              </div>

              {cronStatus.cronJobs && cronStatus.cronJobs.length > 0 ? (
                <div>
                  <span className="text-sm font-medium">Active Jobs:</span>
                  {cronStatus.cronJobs.map((job, index: number) => (
                    <div key={index} className="mt-2 p-2 bg-gray-50 rounded">
                      <div className="text-sm">
                        <strong>{job.jobname}</strong> - {job.schedule}
                      </div>
                      <div className="text-xs text-gray-600">{job.command}</div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-sm text-gray-600">
                  No active cron jobs found
                </div>
              )}
            </div>
          ) : (
            <div className="text-sm text-gray-600">
              Click &quot;Check Status&quot; to view cron job information
            </div>
          )}

          <div className="flex gap-2 mt-4">
            <Button onClick={setupCron} disabled={loading} size="sm">
              <Play className="h-4 w-4 mr-2" />
              Setup Cron
            </Button>
            <Button
              onClick={removeCron}
              disabled={loading}
              variant="outline"
              size="sm"
            >
              <Square className="h-4 w-4 mr-2" />
              Remove Cron
            </Button>
          </div>
        </Card>

        {/* Manual Operations */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Manual Operations</h3>

          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              Trigger an immediate credit reset for all users. This will reset
              credits to 10 for users who haven&apos;t been reset today.
            </p>

            <Button
              onClick={triggerManualReset}
              disabled={loading}
              className="w-full"
            >
              <Users className="h-4 w-4 mr-2" />
              Trigger Manual Reset
            </Button>

            <Button
              onClick={fetchRecentResets}
              disabled={loading}
              variant="outline"
              className="w-full"
            >
              <Clock className="h-4 w-4 mr-2" />
              Load Recent Resets
            </Button>
          </div>
        </Card>
      </div>

      {/* Recent Resets */}
      {recentResets.length > 0 && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Recent Credit Resets</h3>

          <div className="space-y-3">
            {recentResets.map((reset) => (
              <div
                key={reset.id}
                className="flex items-center justify-between p-3 bg-gray-50 rounded"
              >
                <div className="flex items-center gap-3">
                  {getStatusIcon(reset.status)}
                  <div>
                    <div className="text-sm font-medium">
                      {new Date(reset.resetDate).toLocaleString()}
                    </div>
                    <div className="text-xs text-gray-600">
                      {reset.usersAffected} users affected •{" "}
                      {reset.creditsAssigned} credits assigned
                    </div>
                    {reset.errorMessage && (
                      <div className="text-xs text-red-600 mt-1">
                        Error: {reset.errorMessage}
                      </div>
                    )}
                  </div>
                </div>
                {getStatusBadge(reset.status)}
              </div>
            ))}
          </div>
        </Card>
      )}
    </div>
  );
}
