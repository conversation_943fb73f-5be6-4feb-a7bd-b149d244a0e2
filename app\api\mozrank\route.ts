import { NextResponse } from "next/server";

export async function POST(request: Request) {
	try {
		const { url } = await request.json();

		if (!url) {
			return NextResponse.json(
				{ error: "URL is required" },
				{ status: 400 }
			);
		}

		// Note: This is a mock implementation since actual Mozrank API requires credentials
		// You would need to integrate with Moz's API for real data
		const mockResponse = {
			mozrank: Math.random() * 10,
			domain_authority: Math.floor(Math.random() * 100)
		};

		return NextResponse.json(mockResponse);
	} catch (error) {
        console.log(error);
		return NextResponse.json(
			{ error: "Failed to check Moz<PERSON>" },
			{ status: 500 }
		);
	}
}