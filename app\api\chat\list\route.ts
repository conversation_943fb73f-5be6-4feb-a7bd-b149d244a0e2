import { chats } from "@/lib/db/schema";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { eq } from "drizzle-orm";

export async function GET() {
  try {
    const session = await auth();
    if (!session?.user) {
      return new Response("Unauthorized", { status: 401 });
    }

    const userChats = await db
      .select()
      .from(chats)
      .where(eq(chats.userId, session.user.id))
      .orderBy(chats.createdAt);

    return new Response(JSON.stringify(userChats), {
      headers: { "Content-Type": "application/json" },
    });
  } catch (error) {
    console.error("[LIST_CHATS_ERROR]", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}
