"use client";

import { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";
import { Co<PERSON>, Zap, RefreshCw, Clock } from "lucide-react";
import { cn } from "@/lib/utils";
import { Progress } from "@/components/ui/progress";
import type { UserType } from "@/types/index";

interface CreditStatus {
  credits: number;
  lastReset: string;
  needsReset: boolean;
}

interface ExtendedUser {
  id: string;
  email: string;
  name?: string | null;
  userType?: UserType;
}

export function CreditsDisplay() {
  const { data: session } = useSession();
  const [creditStatus, setCreditStatus] = useState<CreditStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCredits = useCallback(async () => {
    if (!session?.user?.id) return;

    try {
      setLoading(true);
      const response = await fetch("/api/user/credits");

      if (!response.ok) {
        throw new Error("Failed to fetch credits");
      }

      const data = await response.json();
      setCreditStatus(data);
      setError(null);
    } catch (err) {
      setError("Failed to load credits");
      console.error("Error fetching credits:", err);
    } finally {
      setLoading(false);
    }
  }, [session?.user?.id]);

  useEffect(() => {
    fetchCredits();
  }, [fetchCredits]);

  // Auto-refresh credits every 30 seconds
  useEffect(() => {
    const interval = setInterval(fetchCredits, 30000);
    return () => clearInterval(interval);
  }, [fetchCredits]);

  if (!session?.user) return null;

  if (loading) {
    return (
      <div className="mx-2 mb-4">
        <div className="relative group">
          {/* Tech border frame */}
          <div className="absolute inset-0 rounded-xl border border-cyan-400/20 bg-gradient-to-r from-cyan-500/5 via-purple-500/5 to-pink-500/5 backdrop-blur-sm" />

          <div className="relative p-4">
            <div className="flex items-center gap-3">
              <div className="relative">
                <div className="absolute inset-0 rounded-lg bg-cyan-400/20 blur-sm animate-pulse" />
                <Coins className="relative h-5 w-5 text-cyan-300" />
              </div>
              <div className="flex-1">
                <div className="h-4 bg-slate-600/50 rounded animate-pulse mb-1" />
                <div className="h-3 bg-slate-700/50 rounded animate-pulse w-2/3" />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="mx-2 mb-4">
        <div className="relative group">
          <div className="absolute inset-0 rounded-xl border border-red-400/20 bg-gradient-to-r from-red-500/5 via-orange-500/5 to-red-500/5 backdrop-blur-sm" />

          <div className="relative p-4">
            <div className="flex items-center gap-3">
              <Coins className="h-5 w-5 text-red-400" />
              <div className="flex-1">
                <p className="text-sm text-red-400">Credits unavailable</p>
                <button
                  onClick={fetchCredits}
                  className="text-xs text-red-300 hover:text-red-200 transition-colors"
                >
                  Retry
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!creditStatus) return null;

  const creditPercentage = Math.min((creditStatus.credits / 10) * 100, 100);
  const isLowCredits = creditStatus.credits <= 2;
  const timeUntilReset = getTimeUntilReset();

  return (
    <div className="mx-2 mb-4">
      <div className="relative group">
        {/* Tech border frame with dynamic colors based on credit level */}
        <div
          className={cn(
            "absolute inset-0 rounded-xl border backdrop-blur-sm transition-all duration-300",
            isLowCredits
              ? "border-orange-400/30 bg-gradient-to-r from-orange-500/10 via-red-500/10 to-orange-500/10"
              : "border-cyan-400/30 bg-gradient-to-r from-cyan-500/10 via-purple-500/10 to-pink-500/10"
          )}
        />

        {/* Corner accents */}
        <div
          className={cn(
            "absolute top-0 left-0 w-3 h-3 border-t-2 border-l-2 rounded-tl-xl transition-colors duration-300",
            isLowCredits ? "border-orange-400" : "border-cyan-400"
          )}
        />
        <div
          className={cn(
            "absolute top-0 right-0 w-3 h-3 border-t-2 border-r-2 rounded-tr-xl transition-colors duration-300",
            isLowCredits ? "border-red-400" : "border-purple-400"
          )}
        />

        <div className="relative p-4">
          {/* Header */}
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <div className="relative">
                <div
                  className={cn(
                    "absolute inset-0 rounded-lg blur-sm transition-colors duration-300",
                    isLowCredits ? "bg-orange-400/20" : "bg-cyan-400/20"
                  )}
                />
                <Coins
                  className={cn(
                    "relative h-5 w-5 transition-colors duration-300",
                    isLowCredits ? "text-orange-300" : "text-cyan-300"
                  )}
                />
              </div>
              <span className="text-sm font-semibold text-white">
                Video Credits
              </span>
            </div>

            {creditStatus.needsReset && (
              <div className="flex items-center gap-1">
                <RefreshCw className="h-3 w-3 text-green-400 animate-spin" />
                <span className="text-xs text-green-400">Resetting...</span>
              </div>
            )}
          </div>

          {/* User Plan Display */}
          {session?.user && (session.user as ExtendedUser).userType && (
            <div className="mb-3">
              <div className="flex items-center gap-2">
                <div className="px-2 py-1 rounded-md bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-400/30">
                  <span className="text-xs font-medium text-purple-300 capitalize">
                    {(
                      (session.user as ExtendedUser).userType as UserType
                    ).replace("-", " ")}{" "}
                    Plan
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Credits count with animation */}
          <div className="flex items-baseline gap-2 mb-3">
            <span
              className={cn(
                "text-2xl font-bold transition-colors duration-300",
                isLowCredits
                  ? "text-orange-300 drop-shadow-lg"
                  : "text-cyan-300 drop-shadow-lg"
              )}
            >
              {creditStatus.credits}
            </span>
            <span className="text-sm text-slate-400">/ 10</span>

            {isLowCredits && (
              <div className="flex items-center gap-1 ml-auto">
                <Zap className="h-3 w-3 text-orange-400 animate-pulse" />
                <span className="text-xs text-orange-400">Low</span>
              </div>
            )}
          </div>

          {/* Progress bar */}
          <div className="mb-3">
            <Progress
              value={creditPercentage}
              className={cn(
                "h-2 transition-all duration-300",
                isLowCredits ? "bg-orange-900/30" : "bg-cyan-900/30"
              )}
            />
          </div>

          {/* Reset timer */}
          <div className="flex items-center gap-2 text-xs">
            <Clock className="h-3 w-3 text-slate-400" />
            <span className="text-slate-400">Resets in {timeUntilReset}</span>
          </div>

          {/* Hover effect overlay */}
          <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-cyan-500/5 via-purple-500/5 to-pink-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </div>
      </div>
    </div>
  );
}

function getTimeUntilReset(): string {
  const now = new Date();
  const tomorrow = new Date(now);
  tomorrow.setDate(tomorrow.getDate() + 1);
  tomorrow.setHours(0, 0, 0, 0); // Midnight UTC

  const diff = tomorrow.getTime() - now.getTime();
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else {
    return `${minutes}m`;
  }
}
