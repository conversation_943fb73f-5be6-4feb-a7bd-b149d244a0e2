import { db } from "@/lib/db";
import { aiModels } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { auth } from "@/lib/auth";

export async function GET() {
  try {
    const session = await auth();
    if (!session?.user) {
      return new Response("Unauthorized", { status: 401 });
    }

    // Fetch active Google Gemini models
    const models = await db
      .select()
      .from(aiModels)
      .where(eq(aiModels.provider, "google"));

    return new Response(JSON.stringify(models), {
      headers: { "content-type": "application/json" },
    });
  } catch (error) {
    console.error("[MODELS_ERROR]", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}
