"use client";

import { ThemeToggle } from "@/components/theme-toggle";
import { But<PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { nanoid } from "nanoid";

interface Chat {
  id: string;
  title: string;
  createdAt: Date;
}

async function fetchChats(): Promise<Chat[]> {
  const response = await fetch("/api/chat/list");
  if (!response.ok) throw new Error("Failed to load chats");
  const data = await response.json();
  return data.map((chat: Chat) => ({
    ...chat,
    createdAt: new Date(chat.createdAt),
  }));
}

async function createChat() {
  const response = await fetch("/api/chat/gemini/create", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      id: nanoid(),
      title: "New Chat",
      model: "gemini-2.5-pro",
    }),
  });
  if (!response.ok) throw new Error("Failed to create chat");
  return response.json();
}

export function ChatSidebar() {
  const pathname = usePathname();
  const router = useRouter();
  const queryClient = useQueryClient();

  const { data: chats = [], isLoading } = useQuery({
    queryKey: ["chats"],
    queryFn: fetchChats,
  });

  const createChatMutation = useMutation({
    mutationFn: createChat,
    onSuccess: (newChat) => {
      queryClient.invalidateQueries({ queryKey: ["chats"] });
      router.push(`/dashboard/chat/gemini/${newChat.id}`);
    },
  });

  return (
    <div className="flex h-full w-80 flex-col bg-muted/50">
      <div className="flex items-center justify-between p-4">
        <Button
          onClick={() => createChatMutation.mutate()}
          className="flex-1"
          variant="secondary"
          disabled={createChatMutation.isPending}
        >
          <Plus className="mr-2 h-4 w-4" />
          {createChatMutation.isPending ? "Creating..." : "New Chat"}
        </Button>
        <div className="ml-2">
          <ThemeToggle />
        </div>
      </div>
      <div className="flex-1 overflow-auto p-2 space-y-2">
        {isLoading ? (
          <div className="flex items-center justify-center py-4">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
          </div>
        ) : chats.length === 0 ? (
          <div className="text-center text-sm text-muted-foreground py-4">
            No chats yet
          </div>
        ) : (
          chats.map((chat) => (
            <Link
              key={chat.id}
              href={`/dashboard/chat/gemini/${chat.id}`}
              className={`block rounded-lg px-3 py-2 text-sm transition-colors hover:bg-accent/50 ${
                pathname.includes(chat.id) ? "bg-accent" : ""
              }`}
            >
              {chat.title || "New Chat"}
            </Link>
          ))
        )}
      </div>
    </div>
  );
}
