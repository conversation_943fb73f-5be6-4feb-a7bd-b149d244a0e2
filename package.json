{"name": "deep-ai-tool", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "PORT=4001 next start", "lint": "next lint", "export": "next export"}, "dependencies": {"@ai-sdk/google": "^1.1.7", "@ai-sdk/openai": "^1.1.5", "@ai-sdk/togetherai": "^0.1.8", "@auth/core": "^0.34.2", "@auth/drizzle-adapter": "^1.7.4", "@fal-ai/client": "^1.6.2", "@google/generative-ai": "^0.21.0", "@hookform/resolvers": "^3.10.0", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.5", "@tanstack/react-query": "^5.66.0", "@types/bcryptjs": "^2.4.6", "@types/react-syntax-highlighter": "^15.5.13", "@vercel/analytics": "^1.4.1", "@vercel/speed-insights": "^1.1.0", "ai": "^4.1.14", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "critters": "^0.0.20", "crypto": "^1.0.1", "dotenv": "^16.4.7", "drizzle-kit": "^0.30.6", "drizzle-orm": "^0.39.3", "jose": "^5.9.6", "lucide-react": "^0.474.0", "nanoid": "^5.0.9", "next": "15.1.6", "next-auth": "^4.24.11", "next-themes": "^0.4.4", "nodemailer": "^6.10.1", "openai": "^4.82.0", "postgres": "^3.4.7", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-markdown": "^9.0.3", "react-syntax-highlighter": "^15.6.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "together-ai": "^0.13.0", "uuid": "^11.1.0", "zod": "^3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20.17.16", "@types/nodemailer": "^6.4.18", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.1.6", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}