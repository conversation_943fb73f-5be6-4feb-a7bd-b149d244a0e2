import { GoogleGenerativeAI } from "@google/generative-ai";
import { NextResponse } from "next/server";

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);

export async function POST(req: Request) {
  try {
    const { url } = await req.json();

    const model = genAI.getGenerativeModel({ model: "gemini-pro" });

    const prompt = `Perform a website security assessment for SEO purposes. Analyze the URL: ${url}
    
    Consider these aspects:
    - General website security best practices
    - SSL/HTTPS implementation
    - Content security measures
    - Basic website hygiene
    
    Format the response as JSON with this structure:
    {
      "status": "secure" | "needs_improvement" | "attention_required",
      "threats": ["list of potential security concerns"],
      "riskLevel": "low" | "medium" | "high",
      "lastScanned": "current_date_time",
      "recommendations": ["list of security improvement suggestions"]
    }
    
    Provide only the JSON response.`;

    const result = await model.generateContent(prompt);

    if (!result.response) {
      throw new Error("No response from AI");
    }

    const text = result.response.text();

    try {
      const jsonResult = JSON.parse(text);
      return NextResponse.json({ result: jsonResult });
    } catch (parseError) {
      console.error("JSON parsing error:", parseError);
      // Provide a fallback response if JSON parsing fails
      return NextResponse.json({
        result: {
          status: "needs_improvement",
          threats: ["Unable to complete full security analysis"],
          riskLevel: "medium",
          lastScanned: new Date().toISOString(),
          recommendations: [
            "Perform manual security audit",
            "Implement regular security monitoring",
            "Consider professional security assessment",
          ],
        },
      });
    }
  } catch (error) {
    console.error("Security checker error:", error);
    return NextResponse.json(
      { error: "Failed to complete security check" },
      { status: 500 }
    );
  }
}
