"use client";

import Image from "next/image";
import Link from "next/link";

interface IconProps {
  className?: string;
}

const ImageModelIcons = {
  Midjourney: ({ className }: IconProps) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-2">
      <Image
        src="/midjourney.svg"
        alt="Midjourney Icon"
        className={className}
        width={96}
        height={96}
      />
    </div>
  ),
  DallE: ({ className }: IconProps) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-2">
      <Image
        src="/dalle.svg"
        alt="DALL-E Icon"
        className={className}
        width={96}
        height={96}
      />
    </div>
  ),
  Flux: ({ className }: IconProps) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-2">
      <Image
        src="/flux.svg"
        alt="Flux Icon"
        className={className}
        width={96}
        height={96}
      />
    </div>
  ),
  Gemini: ({ className }: IconProps) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-2">
      <Image
        src="/gemini.svg"
        alt="Gemini Icon"
        className={className}
        width={96}
        height={96}
      />
    </div>
  ),
  Grok: ({ className }: IconProps) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-2">
      <Image
        src="/grok.svg"
        alt="Grok AI Icon"
        className={className}
        width={96}
        height={96}
      />
    </div>
  ),
};

const imageModels = [
  {
    name: "Grok AI Imagine",
    description: "Grok's advanced AI image generation",
    Icon: ImageModelIcons.Grok,
    href: "/dashboard/image/grok",
    features: ["Creative AI", "Fast Generation", "High Quality"],
    badge: "New",
  },
  {
    name: "Midjourney",
    description: "Create stunning artistic images and illustrations",
    Icon: ImageModelIcons.Midjourney,
    href: "/dashboard/image/midjourney",
    features: ["Artistic Style", "High Detail", "Creative Control"],
    badge: "Pro",
  },
  {
    name: "DALL-E 3",
    description: "OpenAI's advanced image generation model",
    Icon: ImageModelIcons.DallE,
    href: "/dashboard/image/dalle",
    features: ["Photorealistic", "Text Accuracy", "Style Variation"],
    badge: "Pro",
  },
  {
    name: "Flux AI",
    description: "Flux's advanced image generation model",
    Icon: ImageModelIcons.Flux,
    href: "/dashboard/image/flux",
    features: ["Photorealistic", "Text Accuracy", "Style Variation"],
    badge: "Pro",
  },
  {
    name: "Google Gemini",
    description: "Google's multimodal image generation",
    Icon: ImageModelIcons.Gemini,
    href: "/dashboard/image/gemini",
    features: ["Context Aware", "Multiple Styles", "High Resolution"],
    badge: "Pro",
  },
];

export default function ImagePage() {
  return (
    <div className="min-h-screen relative overflow-hidden py-6 sm:py-12">
      <div className="absolute inset-0 dashboard-gradient opacity-20" />
      <div className="relative px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
        <div className="flex flex-col gap-6">
          <div>
            <h1 className="text-4xl font-bold gradient-text mb-2">
              AI Image Generation
            </h1>
            <p className="text-muted-foreground">
              Transform your ideas into stunning visuals with AI
            </p>
          </div>

          <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-2">
            {imageModels.map((model) => (
              <Link key={model.name} href={model.href}>
                <div className="glass-card p-6 h-full hover:ring-2 hover:ring-primary/20 transition-all">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-4">
                      <model.Icon className="h-24 w-24 sm:h-20 sm:w-20" />
                      <h2 className="text-xl font-semibold">{model.name}</h2>
                    </div>
                    {model.badge && (
                      <span className="text-xs font-medium bg-primary/20 text-primary px-2 py-0.5 rounded">
                        {model.badge}
                      </span>
                    )}
                  </div>
                  <p className="text-muted-foreground mb-4">
                    {model.description}
                  </p>
                  <div className="space-y-2">
                    {model.features.map((feature) => (
                      <div
                        key={feature}
                        className="flex items-center gap-2 text-sm text-muted-foreground"
                      >
                        <div className="w-1 h-1 rounded-full bg-primary" />
                        {feature}
                      </div>
                    ))}
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
