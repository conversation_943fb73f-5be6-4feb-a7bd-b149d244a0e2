"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Crown, FileText, Image as ImageIcon, Infinity, MessageSquare, Star, Zap } from "lucide-react";

const unlimitedFeatures = [
  {
    title: "Unlimited AI Chat",
    description: "No limits on AI conversations with all models",
    icon: MessageSquare,
    normalLimit: "100 messages/month",
    unlimitedPerks: ["All AI models access", "Unlimited messages", "Priority response"]
  },
  {
    title: "Unlimited Image Generation",
    description: "Create unlimited AI images in any style",
    icon: ImageIcon,
    normalLimit: "50 images/month",
    unlimitedPerks: ["All image models", "Unlimited generations", "4K resolution"]
  },
  {
    title: "Unlimited SEO Tools",
    description: "Access all SEO tools without restrictions",
    icon: FileText,
    normalLimit: "10 analyses/month",
    unlimitedPerks: ["All 60+ SEO tools", "Unlimited usage", "Detailed reports"]
  }
];

export default function BonusPage() {
  return (
    <div className="min-h-screen relative overflow-hidden py-6 sm:py-12">
      <div className="absolute inset-0 dashboard-gradient opacity-20" />
      <div className="relative px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
        <div className="flex flex-col gap-6">
          <div className="text-center">
            <h1 className="text-4xl font-bold gradient-text mb-2">Unlimited Features</h1>
            <p className="text-muted-foreground text-lg">
              Unlock unlimited access to all our powerful AI tools
            </p>
          </div>

          {/* Hero Card */}
          <div className="glass-card p-8 text-center mb-6">
            <div className="inline-block p-3 rounded-full bg-primary/10 mb-4">
              <Infinity className="h-8 w-8 text-primary" />
            </div>
            <h2 className="text-2xl font-bold mb-2">Unlimited Plan Benefits</h2>
            <p className="text-muted-foreground mb-6">
              Remove all limits and unlock the full potential of AI
            </p>
            <Button size="lg" className="bg-primary hover:bg-primary/90">
              <Crown className="mr-2 h-5 w-5" />
              Upgrade to Unlimited
            </Button>
          </div>

          {/* Features Grid */}
          <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
            {unlimitedFeatures.map((feature) => (
              <div key={feature.title} className="glass-card p-6 h-full">
                <div className="flex items-center gap-3 mb-4">
                  <feature.icon className="h-6 w-6 text-primary" />
                  <h2 className="text-xl font-semibold">{feature.title}</h2>
                </div>
                <p className="text-muted-foreground mb-4">{feature.description}</p>
                
                {/* Free Limit */}
                <div className="p-3 rounded-lg bg-white/5 mb-4">
                  <p className="text-sm text-muted-foreground">
                    Free Plan Limit: <span className="text-primary">{feature.normalLimit}</span>
                  </p>
                </div>

                {/* Unlimited Benefits */}
                <div className="space-y-2">
                  {feature.unlimitedPerks.map((perk, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm">
                      <Star className="h-4 w-4 text-primary flex-shrink-0" />
                      <span>{perk}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>

          {/* Bottom CTA */}
          <div className="glass-card p-6 mt-6">
            <div className="flex flex-col md:flex-row items-center justify-between gap-4">
              <div>
                <h3 className="text-xl font-semibold mb-2">Ready to go Unlimited?</h3>
                <p className="text-muted-foreground">
                  Upgrade now and experience AI without limits
                </p>
              </div>
              <Button size="lg" className="bg-primary hover:bg-primary/90 whitespace-nowrap">
                <Zap className="mr-2 h-5 w-5" />
                Get Unlimited Access
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
