{"id": "addc97d9-2ab0-4501-8d84-dc500c75fffd", "prevId": "e4842b6a-dcbf-4217-915c-841170d66c8a", "version": "7", "dialect": "postgresql", "tables": {"public.accounts": {"name": "accounts", "schema": "", "columns": {"userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": true}, "providerAccountId": {"name": "providerAccountId", "type": "text", "primaryKey": false, "notNull": true}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false}, "token_type": {"name": "token_type", "type": "text", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "session_state": {"name": "session_state", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"accounts_userId_users_id_fk": {"name": "accounts_userId_users_id_fk", "tableFrom": "accounts", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"accounts_provider_providerAccountId_pk": {"name": "accounts_provider_providerAccountId_pk", "columns": ["provider", "providerAccountId"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ai_models": {"name": "ai_models", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": true}, "model": {"name": "model", "type": "text", "primaryKey": false, "notNull": true}, "max_tokens": {"name": "max_tokens", "type": "integer", "primaryKey": false, "notNull": true}, "temperature": {"name": "temperature", "type": "text", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chats": {"name": "chats", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false}, "model": {"name": "model", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"chats_userId_users_id_fk": {"name": "chats_userId_users_id_fk", "tableFrom": "chats", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.messages": {"name": "messages", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "chatId": {"name": "chatId", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"messages_chatId_chats_id_fk": {"name": "messages_chatId_chats_id_fk", "tableFrom": "messages", "tableTo": "chats", "columnsFrom": ["chatId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.password_reset_tokens": {"name": "password_reset_tokens", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "expiresAt": {"name": "expiresAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "used": {"name": "used", "type": "text", "primaryKey": false, "notNull": true, "default": "'false'"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"sessionToken": {"name": "sessionToken", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"sessions_userId_users_id_fk": {"name": "sessions_userId_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}, "emailVerified": {"name": "emailVerified", "type": "timestamp", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "userType": {"name": "userType", "type": "text", "primaryKey": false, "notNull": true}, "createdBy": {"name": "created<PERSON>y", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verificationToken": {"name": "verificationToken", "schema": "", "columns": {"identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"verificationToken_identifier_token_pk": {"name": "verificationToken_identifier_token_pk", "columns": ["identifier", "token"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}