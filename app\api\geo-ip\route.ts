import { NextResponse } from "next/server";

interface GeoIPResult {
	ip: string;
	country: string;
	city: string;
	latitude: number;
	longitude: number;
	error?: string;
}

export async function POST(request: Request) {
	try {
		const { ips } = await request.json();

		if (!ips || !Array.isArray(ips)) {
			return NextResponse.json(
				{ error: "IP addresses are required" },
				{ status: 400 }
			);
		}

		// Process each IP address
		const results: GeoIPResult[] = await Promise.all(
			ips.map(async (ip) => {
				try {
					// Use a free IP geolocation API
					const response = await fetch(`http://ip-api.com/json/${ip}`);
					const data = await response.json();

					if (data.status === 'success') {
						return {
							ip,
							country: data.country,
							city: data.city,
							latitude: data.lat,
							longitude: data.lon,
						};
					} else {
						return {
							ip,
							country: 'Unknown',
							city: 'Unknown',
							latitude: 0,
							longitude: 0,
							error: 'Location not found',
						};
					}
				} catch (error) {
                    console.log(error);
					return {
						ip,
						country: 'Error',
						city: 'Error',
						latitude: 0,
						longitude: 0,
						error: 'Failed to fetch location',
					};
				}
			})
		);

		return NextResponse.json({ results });
	} catch (error) {
        console.log(error);
		return NextResponse.json(
			{ error: "Failed to process IP addresses" },
			{ status: 500 }
		);
	}
}