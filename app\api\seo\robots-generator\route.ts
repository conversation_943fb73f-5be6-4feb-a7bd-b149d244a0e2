import { GoogleGenerativeAI } from "@google/generative-ai";
import { NextResponse } from "next/server";

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);

function cleanJsonResponse(text: string): string {
	text = text.replace(/```txt\n/g, '').replace(/```/g, '');
	return text.trim();
}

export async function POST(req: Request) {
	try {
		const { siteUrl, allowAll, disallowPaths, sitemap } = await req.json();
		
		const model = genAI.getGenerativeModel({ model: "gemini-pro" });
		
		const prompt = `Generate a robots.txt file for the following website with these specifications:

Website URL: ${siteUrl}
Allow All Crawling: ${allowAll ? 'Yes' : 'No'}
Disallow Paths:
${disallowPaths || 'None'}
Sitemap URL: ${sitemap || 'None'}

Please generate a properly formatted robots.txt file following best practices and SEO guidelines. Return only the robots.txt content without any additional text or formatting.`;

		const result = await model.generateContent(prompt);
		const response = await result.response;
		const text = response.text();
		
		const cleanedText = cleanJsonResponse(text);

		return NextResponse.json({ result: cleanedText });
	} catch (error) {
		console.error("Robots.txt generator error:", error);
		return NextResponse.json(
			{ error: "Failed to generate robots.txt" },
			{ status: 500 }
		);
	}
}