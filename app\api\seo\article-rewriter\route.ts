import { GoogleGenerativeAI } from "@google/generative-ai";
import { NextResponse } from "next/server";

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);

export async function POST(req: Request) {
	try {
		const { content } = await req.json();
		
		const model = genAI.getGenerativeModel({ model: "gemini-pro" });
		
		const prompt = `Please rewrite the following article while maintaining its meaning but improving its quality, readability, and uniqueness. Make it more engaging while keeping the main points intact:

${content}

Please provide only the rewritten article without any additional comments or explanations.`;

		const result = await model.generateContent(prompt);
		const response = await result.response;
		const text = response.text();

		return NextResponse.json({ result: text });
	} catch (error) {
		console.error("Article rewriter error:", error);
		return NextResponse.json(
			{ error: "Failed to rewrite article" },
			{ status: 500 }
		);
	}
}