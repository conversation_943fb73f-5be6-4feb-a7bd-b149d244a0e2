import { GoogleGenerativeAI } from "@google/generative-ai";
import { NextResponse } from "next/server";

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);

export async function POST(req: Request) {
  try {
    const { domain } = await req.json();

    const model = genAI.getGenerativeModel({ model: "gemini-2.5-pro" });

    const prompt = `Analyze the domain ${domain} and provide IP and DNS information. Format the response as JSON with this structure (respond with only the JSON, no markdown or code blocks):
    {
      "domain": "${domain}",
      "ipAddresses": [
        {
          "ip": "xxx.xxx.xxx.xxx",
          "type": "IPv4",
          "location": {
            "country": "United States",
            "city": "San Francisco",
            "region": "California"
          },
          "provider": "Example Provider"
        }
      ],
      "dns": [
        {
          "hostname": "${domain}",
          "type": "A",
          "value": "xxx.xxx.xxx.xxx"
        }
      ],
      "additionalInfo": {
        "reverseDns": "host.example.com",
        "isProxy": false,
        "securityInfo": [
          "SSL enabled",
          "No blacklist records found"
        ]
      }
    }`;

    const result = await model.generateContent(prompt);

    if (!result.response) {
      throw new Error("No response from AI");
    }

    const text = result.response.text();

    try {
      // Clean the response text
      const cleanText = text.replace(/```json\n?|\n?```/g, "").trim();
      const jsonResult = JSON.parse(cleanText);

      // Ensure arrays are always arrays
      if (!Array.isArray(jsonResult.ipAddresses)) {
        jsonResult.ipAddresses = [jsonResult.ipAddresses].filter(Boolean);
      }
      if (!Array.isArray(jsonResult.dns)) {
        jsonResult.dns = [jsonResult.dns].filter(Boolean);
      }
      if (
        jsonResult.additionalInfo?.securityInfo &&
        !Array.isArray(jsonResult.additionalInfo.securityInfo)
      ) {
        jsonResult.additionalInfo.securityInfo = [
          jsonResult.additionalInfo.securityInfo,
        ].filter(Boolean);
      }

      return NextResponse.json({ result: jsonResult });
    } catch (parseError) {
      console.error("JSON parsing error:", parseError);
      // Fallback response
      return NextResponse.json({
        result: {
          domain: domain,
          ipAddresses: [
            {
              ip: "*********",
              type: "IPv4",
              location: {
                country: "Unknown",
                city: "Unknown",
                region: "Unknown",
              },
              provider: "Unknown Provider",
            },
          ],
          dns: [
            {
              hostname: domain,
              type: "A",
              value: "*********",
            },
          ],
          additionalInfo: {
            reverseDns: null,
            isProxy: false,
            securityInfo: ["Information not available"],
          },
        },
      });
    }
  } catch (error) {
    console.error("Domain to IP converter error:", error);
    return NextResponse.json(
      { error: "Failed to convert domain to IP" },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({ error: "Method not allowed" }, { status: 405 });
}
