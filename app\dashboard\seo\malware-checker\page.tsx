"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Loader2 } from "lucide-react";
import { Card } from "@/components/ui/card";

interface MalwareResult {
  status: string;
  threats: string[];
  riskLevel: "low" | "medium" | "high";
  lastScanned: string;
  recommendations: string[];
}

export default function MalwareCheckerPage() {
  const [url, setUrl] = useState("");
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<MalwareResult | null>(null);
  const { toast } = useToast();

  const handleAnalyze = async () => {
    if (!url.trim()) {
      toast({
        title: "Error",
        description: "Please enter a valid URL to analyze",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);
      const response = await fetch("/api/seo/malware-checker", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ url }),
      });

      if (!response.ok) {
        throw new Error("Failed to analyze website");
      }

      const data = await response.json();
      setResult(data.result);
    } catch (err) {
      console.error("An error occurred:", err);
      toast({
        title: "Error",
        description: "Failed to analyze website. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Malware Checker</h1>
      <div className="grid gap-6">
        <div>
          <label className="block mb-2">Website URL</label>
          <Input
            type="url"
            placeholder="Enter website URL..."
            value={url}
            onChange={(e) => setUrl(e.target.value)}
          />
        </div>
        <Button
          onClick={handleAnalyze}
          disabled={loading}
          className="w-full md:w-auto"
        >
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Analyzing...
            </>
          ) : (
            "Check for Malware"
          )}
        </Button>

        {result && (
          <div className="space-y-6">
            <Card className="p-4">
              <h2 className="text-lg font-semibold mb-4">Scan Results</h2>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-secondary/20 rounded">
                  <div>
                    <p className="font-medium">Security Status</p>
                    <p className="text-sm text-muted-foreground">
                      Last Scanned: {result.lastScanned}
                    </p>
                  </div>
                  <span
                    className={`px-2 py-1 rounded text-sm ${
                      result.riskLevel === "low"
                        ? "bg-green-100 text-green-800"
                        : result.riskLevel === "medium"
                        ? "bg-yellow-100 text-yellow-800"
                        : "bg-red-100 text-red-800"
                    }`}
                  >
                    {result.status}
                  </span>
                </div>

                {result.threats.length > 0 && (
                  <div className="mt-4">
                    <h3 className="font-medium mb-2">Detected Threats</h3>
                    <ul className="list-disc pl-5 space-y-2">
                      {result.threats.map((threat, index) => (
                        <li key={index} className="text-red-600">
                          {threat}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {result.recommendations.length > 0 && (
                  <div className="mt-6">
                    <h3 className="font-medium mb-2">Recommendations</h3>
                    <ul className="list-disc pl-5 space-y-2">
                      {result.recommendations.map((recommendation, index) => (
                        <li key={index} className="text-muted-foreground">
                          {recommendation}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}
