import { GoogleGenerativeAI } from "@google/generative-ai";
import { NextResponse } from "next/server";

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);

export async function POST(req: Request) {
  try {
    const { content } = await req.json();

    const model = genAI.getGenerativeModel({ model: "gemini-2.5-pro" });

    const prompt = `Analyze the following text for potential plagiarism. Provide a similarity score (as a percentage) and identify any matching content from known sources. Format the response as JSON with the following structure:
		{
			"similarity": number,
			"matches": [
				{
					"text": "matched text",
					"source": "source URL or description"
				}
			]
		}

		Text to analyze:
		${content}

		Provide only the JSON response without any additional text.`;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    // Parse the response as JSON
    const jsonResult = JSON.parse(text);

    return NextResponse.json({ result: jsonResult });
  } catch (error) {
    console.error("Plagiarism checker error:", error);
    return NextResponse.json(
      { error: "Failed to check plagiarism" },
      { status: 500 }
    );
  }
}
