import { GoogleGenerativeAI } from "@google/generative-ai";
import { NextResponse } from "next/server";

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);

function cleanJsonResponse(text: string): string {
	// Remove markdown code blocks if present
	text = text.replace(/```json\n/g, '').replace(/```/g, '');
	// Remove any leading/trailing whitespace
	text = text.trim();
	return text;
}

export async function POST(req: Request) {
	try {
		const { url } = await req.json();
		
		const model = genAI.getGenerativeModel({ model: "gemini-pro" });
		
		const prompt = `Analyze the meta tags of the following website URL and provide a detailed analysis. Return the response in this exact JSON format without any markdown formatting or additional text:
		{
			"title": {
				"value": "current title",
				"score": score (0-100),
				"suggestions": ["suggestion1", "suggestion2"]
			},
			"description": {
				"value": "current description",
				"score": score (0-100),
				"suggestions": ["suggestion1", "suggestion2"]
			},
			"keywords": {
				"value": ["keyword1", "keyword2"],
				"score": score (0-100),
				"suggestions": ["suggestion1", "suggestion2"]
			},
			"overall": overall score (0-100)
		}

		URL: ${url}`;

		const result = await model.generateContent(prompt);
		const response = await result.response;
		const text = response.text();
		
		// Clean and parse the response
		const cleanedText = cleanJsonResponse(text);
		const jsonResult = JSON.parse(cleanedText);

		return NextResponse.json({ result: jsonResult });
	} catch (error) {
		console.error("Meta analyzer error:", error);
		return NextResponse.json(
			{ error: "Failed to analyze meta tags" },
			{ status: 500 }
		);
	}
}