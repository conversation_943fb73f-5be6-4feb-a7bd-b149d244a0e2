import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { messages } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

export async function GET(req: Request) {
	try {
		const session = await auth();
		if (!session?.user) {
			return new Response(JSON.stringify({ error: "Unauthorized" }), {
				status: 401,
			});
		}

		const { searchParams } = new URL(req.url);
		const chatId = searchParams.get("chatId");

		if (!chatId) {
			return new Response(JSON.stringify({ error: "Chat ID is required" }), {
				status: 400,
			});
		}

		const chatMessages = await db
			.select()
			.from(messages)
			.where(eq(messages.chatId, chatId))
			.orderBy(messages.createdAt);

		return new Response(JSON.stringify(chatMessages), {
			headers: {
				"Content-Type": "application/json",
			},
		});
	} catch (error) {
		console.error("[MESSAGES_ERROR]", error);
		return new Response(
			JSON.stringify({
				error: error instanceof Error ? error.message : "Internal server error",
			}),
			{ status: 500 }
		);
	}
}
