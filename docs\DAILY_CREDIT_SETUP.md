# Daily Credit Reset System Setup

This document explains how to set up the automatic daily credit reset system using PostgreSQL's `pg_cron` extension.

## Overview

The system automatically resets all users' credits to 10 every day at midnight UTC. It uses PostgreSQL's native `pg_cron` extension for reliable, database-level scheduling.

## Features

- ✅ Automatic daily credit reset at midnight UTC
- ✅ Only resets users who haven't been reset today
- ✅ Comprehensive logging in `daily_credit_resets` table
- ✅ Error handling and recovery
- ✅ Manual trigger capability for admins
- ✅ Works with any PostgreSQL database that supports `pg_cron`

## Database Requirements

### Option 1: Self-hosted PostgreSQL
If you're running your own PostgreSQL server:
```sql
-- Enable pg_cron extension (requires superuser)
CREATE EXTENSION IF NOT EXISTS pg_cron;
```

### Option 2: Managed Database Services

**Neon Database:**
- pg_cron is available on Neon Pro plans
- Contact support to enable it

**Supabase:**
- pg_cron is available on Pro plans and above
- Enable via dashboard or contact support

**AWS RDS:**
- Available on PostgreSQL 12+ with shared_preload_libraries
- Requires parameter group modification

**Google Cloud SQL:**
- Available with cloudsql-pgcron extension
- Enable via console or gcloud CLI

**Azure Database:**
- Available as pg_cron extension
- Enable via portal or CLI

## Setup Instructions

### 1. Run Database Migration

```bash
# Apply the migration that creates the credit system
npx drizzle-kit push:pg
```

This will:
- Add `lastCreditReset` column to users table
- Create `daily_credit_resets` tracking table
- Create the reset functions
- Attempt to schedule the cron job

### 2. Verify pg_cron Extension

Check if pg_cron is available:
```sql
SELECT * FROM pg_extension WHERE extname = 'pg_cron';
```

If not available, enable it (requires superuser):
```sql
CREATE EXTENSION IF NOT EXISTS pg_cron;
```

### 3. Setup Cron Job

#### Option A: Automatic Setup (Recommended)
Use the admin API endpoint:
```bash
# Setup the cron job
curl -X POST /api/admin/cron-setup \
  -H "Content-Type: application/json" \
  -d '{"action": "setup"}' \
  -H "Cookie: your-auth-cookie"
```

#### Option B: Manual Setup
Run this SQL directly:
```sql
SELECT setup_daily_credit_cron();
```

### 4. Verify Setup

Check cron job status:
```bash
curl -X GET /api/admin/cron-setup \
  -H "Cookie: your-auth-cookie"
```

Or check directly in database:
```sql
SELECT * FROM cron.job WHERE jobname = 'daily-credit-reset';
```

## Manual Operations

### Trigger Manual Reset
```bash
# Trigger immediate credit reset
curl -X POST /api/admin/reset-daily-credits \
  -H "Cookie: your-auth-cookie"
```

Or via SQL:
```sql
SELECT * FROM manual_reset_daily_credits();
```

### Remove Cron Job
```bash
curl -X POST /api/admin/cron-setup \
  -H "Content-Type: application/json" \
  -d '{"action": "remove"}' \
  -H "Cookie: your-auth-cookie"
```

## Monitoring

### Check Recent Resets
```sql
SELECT * FROM daily_credit_resets 
ORDER BY createdAt DESC 
LIMIT 10;
```

### Check User Credit Status
```sql
SELECT 
  email,
  credits,
  lastCreditReset,
  DATE(lastCreditReset AT TIME ZONE 'UTC') as reset_date
FROM users 
ORDER BY lastCreditReset DESC;
```

### View Cron Job Logs
```sql
-- View cron job execution history
SELECT * FROM cron.job_run_details 
WHERE jobname = 'daily-credit-reset' 
ORDER BY start_time DESC;
```

## Troubleshooting

### pg_cron Not Available
If pg_cron is not available on your database:

1. **Contact your database provider** to enable it
2. **Use alternative scheduling** (see Alternative Methods below)
3. **Upgrade your database plan** if needed

### Cron Job Not Running
1. Check if extension is enabled: `SELECT * FROM pg_extension WHERE extname = 'pg_cron';`
2. Verify job is scheduled: `SELECT * FROM cron.job;`
3. Check job logs: `SELECT * FROM cron.job_run_details;`
4. Ensure database user has necessary permissions

### Credits Not Resetting
1. Check `daily_credit_resets` table for error messages
2. Run manual reset to test: `SELECT * FROM manual_reset_daily_credits();`
3. Verify user `lastCreditReset` timestamps

## Alternative Methods

If pg_cron is not available, you can use:

### 1. External Cron Job
Set up a system cron job to call the API:
```bash
# Add to crontab (runs at midnight UTC)
0 0 * * * curl -X POST https://yourapp.com/api/admin/reset-daily-credits -H "Authorization: Bearer YOUR_SECRET"
```

### 2. Vercel Cron (for Vercel deployments)
Add to `vercel.json`:
```json
{
  "crons": [
    {
      "path": "/api/admin/reset-daily-credits",
      "schedule": "0 0 * * *"
    }
  ]
}
```

### 3. GitHub Actions
Create `.github/workflows/daily-credit-reset.yml`:
```yaml
name: Daily Credit Reset
on:
  schedule:
    - cron: '0 0 * * *'  # Midnight UTC
jobs:
  reset-credits:
    runs-on: ubuntu-latest
    steps:
      - name: Reset Credits
        run: |
          curl -X POST ${{ secrets.APP_URL }}/api/admin/reset-daily-credits \
            -H "Authorization: Bearer ${{ secrets.CRON_SECRET }}"
```

## Security Notes

- Only admin users can trigger manual resets
- Cron job runs with database privileges
- All operations are logged for audit trail
- Failed resets are recorded with error messages

## Configuration

The system uses these default values:
- **Daily Credits**: 10 (configurable in the SQL function)
- **Reset Time**: Midnight UTC (configurable in cron schedule)
- **Timezone**: UTC (recommended for consistency)

To change the daily credit amount, modify the `credits_amount` variable in the `reset_daily_credits()` function.
