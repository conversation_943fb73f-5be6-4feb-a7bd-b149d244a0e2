"use client";

import * as React from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Message } from "ai";
import { useChat } from "ai/react";
import { AlertCircle, Bot, Loader2, Send } from "lucide-react";
import { useParams } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { ChatMessage } from "./chat-message";

// Define the structure of the message from the API
interface DbMessage {
  id: string;
  role: string;
  content: string;
  createdAt: string;
}

interface StreamMessage {
  id: string;
  role: "assistant";
  content: string;
}

export function DeepSeekChatContainer(): React.ReactElement {
  const params = useParams();
  const chatId = params?.chatId as string;
  const { toast } = useToast();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const isSubmitting = useRef(false);
  const [isThinking, setIsThinking] = useState(false);
  const queryClient = useQueryClient();

  // Get messages from the database
  const { data: dbMessages, isLoading: isLoadingMessages } = useQuery<
    Message[],
    Error
  >({
    queryKey: ["messages", chatId],
    queryFn: async () => {
      const response = await fetch(
        `/api/chat/deepseek/messages?chatId=${chatId}`
      );
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to load messages");
      }
      const data = await response.json();
      return data.map((msg: DbMessage) => ({
        id: msg.id,
        role: msg.role as "user" | "assistant",
        content: msg.content,
        createdAt: new Date(msg.createdAt),
      }));
    },
  });

  const {
    messages,
    input,
    handleInputChange,
    handleSubmit: aiSubmit,
    isLoading,
    error,
    setMessages,
  } = useChat({
    api: "/api/chat/deepseek",
    id: chatId,
    initialMessages: dbMessages || [],
    body: {
      chatId,
      model: "deepseek-ai/DeepSeek-V3",
    } as const,
    onResponse: async (response: Response) => {
      if (!response.ok) {
        if (response.status === 401) {
          throw new Error("Unauthorized. Please check your API key.");
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("No readable stream available");
      }

      try {
        const decoder = new TextDecoder();
        let currentMessageId = "";
        let currentContent = "";
        let lastActivityTime = Date.now();
        const TIMEOUT_DURATION = 30000;
        const MAX_RETRIES = 3;
        let retryCount = 0;
        setIsThinking(true);

        const checkTimeout = () => {
          const now = Date.now();
          if (now - lastActivityTime > TIMEOUT_DURATION) {
            throw new Error("Stream timeout - No data received for 30 seconds");
          }
        };

        let shouldContinue = true;
        while (shouldContinue) {
          try {
            checkTimeout();
            const { done, value } = await reader.read();
            if (done) {
              shouldContinue = false;
              break;
            }

            lastActivityTime = Date.now();
            const chunk = decoder.decode(value);
            const lines = chunk.split("\n");

            for (const line of lines) {
              if (line.startsWith("data: ")) {
                const data = line.slice(5).trim();
                if (data === "[DONE]") continue;

                try {
                  const parsed = JSON.parse(data) as StreamMessage;
                  if (parsed.id && parsed.role === "assistant") {
                    if (currentMessageId !== parsed.id) {
                      currentMessageId = parsed.id;
                      currentContent = "";
                    }

                    if (parsed.content.includes("<think>")) {
                      continue;
                    }
                    if (parsed.content.includes("</think>")) {
                      setIsThinking(false);
                      continue;
                    }

                    if (
                      !parsed.content.includes("<think>") &&
                      !parsed.content.includes("</think>")
                    ) {
                      currentContent += parsed.content;
                      setMessages((prev: Message[]) => {
                        const lastMessage = prev[prev.length - 1];
                        if (lastMessage?.id === currentMessageId) {
                          return prev.map((msg) =>
                            msg.id === currentMessageId
                              ? { ...msg, content: currentContent }
                              : msg
                          );
                        }
                        return [
                          ...prev,
                          {
                            id: currentMessageId,
                            role: "assistant" as const,
                            content: currentContent,
                            createdAt: new Date(),
                          } as Message,
                        ];
                      });
                    }
                  }
                } catch (e: unknown) {
                  console.error("Error parsing chunk:", e);
                }
              }
            }
          } catch (error: unknown) {
            console.error("Stream reading error:", error);
            if (retryCount < MAX_RETRIES) {
              retryCount++;
              console.log(
                `Retrying stream connection (${retryCount}/${MAX_RETRIES})...`
              );
              await new Promise((resolve) => setTimeout(resolve, 1000));
              continue;
            }
            shouldContinue = false;
            throw error;
          }
        }
      } finally {
        try {
          reader.releaseLock();
        } catch (e: unknown) {
          console.error("Error releasing reader lock:", e);
        }
        setIsThinking(false);
      }
    },
    onFinish: async () => {
      setIsThinking(false);
      await queryClient.invalidateQueries({ queryKey: ["messages", chatId] });
    },
    onError: (error: Error) => {
      console.error("AI Chat error details:", error);
      setIsThinking(false);
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Update messages when database messages change
  useEffect(() => {
    if (dbMessages?.length && messages.length === 0) {
      setMessages(dbMessages);
    }
  }, [dbMessages, messages.length, setMessages]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const handleKeyDown = async (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      if (isLoading || !input.trim() || isSubmitting.current) return;

      isSubmitting.current = true;
      try {
        // Create a synthetic form event and call aiSubmit directly
        const syntheticEvent = {
          preventDefault: () => {},
          currentTarget: e.currentTarget.form,
        } as React.FormEvent<HTMLFormElement>;
        void aiSubmit(syntheticEvent);
      } catch (error: unknown) {
        console.error("Submit error:", error);
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to send message. Please try again.",
        });
      } finally {
        isSubmitting.current = false;
      }
    }
  };

  const handleFormSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (isLoading || !input.trim() || isSubmitting.current) return;

    isSubmitting.current = true;
    try {
      void aiSubmit(e);
    } catch (error: unknown) {
      console.error("Submit error:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to send message. Please try again.",
      });
    } finally {
      isSubmitting.current = false;
    }
  };

  return (
    <div className="flex h-full flex-col">
      <Card className="rounded-none border-x-0 border-t-0 bg-background/95 dark:bg-background/95">
        <div className="flex items-center justify-between border-b px-4 py-3 dark:border-border">
          <div className="flex items-center gap-2">
            <Bot className="h-5 w-5 text-primary dark:text-primary" />
            <h2 className="text-lg font-semibold tracking-tight text-foreground dark:text-foreground">
              Chat with DeepSeek
            </h2>
          </div>
        </div>
      </Card>

      <ScrollArea className="flex-1 px-4">
        <div className="space-y-4 py-4">
          {error ? (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error.message}</AlertDescription>
            </Alert>
          ) : isLoadingMessages ? (
            <div className="flex h-[60vh] flex-col items-center justify-center gap-2">
              <Loader2 className="h-12 w-12 animate-spin text-muted-foreground dark:text-muted-foreground" />
              <p className="text-sm text-muted-foreground dark:text-muted-foreground">
                Loading messages...
              </p>
            </div>
          ) : messages.length === 0 ? (
            <div className="flex h-[60vh] flex-col items-center justify-center gap-2">
              <Bot className="h-12 w-12 text-muted-foreground dark:text-muted-foreground" />
              <h3 className="text-xl font-semibold text-foreground dark:text-foreground">
                Welcome to DeepSeek Chat
              </h3>
              <p className="text-sm text-muted-foreground dark:text-muted-foreground">
                Start a conversation by typing a message below.
              </p>
            </div>
          ) : (
            <>
              {messages.map((message) => (
                <ChatMessage key={message.id} message={message} />
              ))}
              {isThinking && (
                <div className="flex items-center gap-2 px-4 py-2 rounded bg-muted/50 dark:bg-muted">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <p className="text-sm text-muted-foreground dark:text-muted-foreground">
                    DeepSeek is thinking...
                  </p>
                </div>
              )}
            </>
          )}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      <Card className="rounded-none border-x-0 border-b-0 px-4 py-3 bg-background/95 dark:bg-background/95">
        <form onSubmit={handleFormSubmit} className="flex gap-3" noValidate>
          <Textarea
            value={input}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder="Type your message... (Press Enter to send, Shift+Enter for new line)"
            className="min-h-[60px] resize-none bg-background dark:bg-muted focus:bg-background dark:focus:bg-muted"
            disabled={isLoading}
          />
          <Button
            type="submit"
            size="icon"
            disabled={isLoading || !input.trim()}
            className="dark:hover:bg-primary/90"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
            <span className="sr-only">Send message</span>
          </Button>
        </form>
      </Card>
    </div>
  );
}
