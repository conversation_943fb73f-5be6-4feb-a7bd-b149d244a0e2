"use client";

import { nanoid } from "nanoid";
import { useRouter } from "next/navigation";
import { Loader2 } from "lucide-react";
import { useEffect } from "react";

export default function DeepSeekChatPage() {
	const router = useRouter();

	useEffect(() => {
		const tempChatId = nanoid();
		// Use replace instead of push to avoid adding to history stack
		router.replace(`/dashboard/chat/deepseek/${tempChatId}`);
	}, [router]); // Include router in dependencies as recommended by React

	return (
		<div className="flex h-[calc(100vh-4rem)] items-center justify-center">
			<Loader2 className="h-8 w-8 animate-spin text-primary" />
		</div>
	);
}
