import { createDeepSeekStreamParser } from './stream-parser';

export async function processDeepSeekStream(
	stream: ReadableStream<Uint8Array>,
): Promise<string> {
	const parser = createDeepSeekStreamParser();
	const reader = stream.getReader();
	const decoder = new TextDecoder();
	let result = '';

	try {
		while (true) {
			const { done, value } = await reader.read();
			if (done) break;

			const chunk = decoder.decode(value);
			const parsed = parser.parseStreamPart(chunk);
			if (parsed.trim()) {
				result += parsed;
			}
		}
	} finally {
		reader.releaseLock();
	}

	return result;
}