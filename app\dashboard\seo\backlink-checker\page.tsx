"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Loader2 } from "lucide-react";
import { Card } from "@/components/ui/card";

interface Backlink {
	url: string;
	anchorText: string;
	domainAuthority: number;
	dofollow: boolean;
}

export default function BacklinkCheckerPage() {
	const [url, setUrl] = useState("");
	const [loading, setLoading] = useState(false);
	const [results, setResults] = useState<{
		totalBacklinks: number;
		domainAuthority: number;
		backlinks: Backlink[];
	} | null>(null);
	const { toast } = useToast();

	const handleCheck = async () => {
		if (!url.trim()) {
			toast({
				title: "Error",
				description: "Please enter a website URL",
				variant: "destructive",
			});
			return;
		}

		try {
			setLoading(true);
			const response = await fetch("/api/seo/backlink-checker", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ url }),
			});

			if (!response.ok) {
				throw new Error("Failed to check backlinks");
			}

			const data = await response.json();
			setResults(data.result);
		} catch (err) {
            console.error("An error occurred:", err);
			toast({
				title: "Error",
				description: "Failed to check backlinks. Please try again.",
				variant: "destructive",
			});
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="container mx-auto py-8">
			<h1 className="text-3xl font-bold mb-6">Backlink Checker</h1>
			<div className="grid gap-6">
				<div className="flex gap-4">
					<Input
						placeholder="Enter website URL"
						value={url}
						onChange={(e) => setUrl(e.target.value)}
						className="flex-1"
					/>
					<Button 
						onClick={handleCheck} 
						disabled={loading}
					>
						{loading ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								Checking...
							</>
						) : (
							"Check Backlinks"
						)}
					</Button>
				</div>
				
				{results && (
					<div className="space-y-6">
						<Card className="p-6">
							<div className="grid grid-cols-2 gap-4">
								<div>
									<h3 className="text-sm font-medium">Total Backlinks</h3>
									<p className="text-2xl font-bold">{results.totalBacklinks}</p>
								</div>
								<div>
									<h3 className="text-sm font-medium">Domain Authority</h3>
									<p className="text-2xl font-bold">{results.domainAuthority}</p>
								</div>
							</div>
						</Card>

						<div className="space-y-4">
							<h2 className="text-xl font-semibold">Top Backlinks</h2>
							{results.backlinks.map((backlink, index) => (
								<Card key={index} className="p-4">
									<div className="space-y-2">
										<div className="flex justify-between items-center">
											<a 
												href={backlink.url} 
												target="_blank" 
												rel="noopener noreferrer"
												className="text-primary hover:underline"
											>
												{backlink.url}
											</a>
											<span className={backlink.dofollow ? "text-green-600" : "text-yellow-600"}>
												{backlink.dofollow ? "Dofollow" : "Nofollow"}
											</span>
										</div>
										<p className="text-sm text-muted-foreground">
											Anchor Text: {backlink.anchorText}
										</p>
										<p className="text-sm">
											Domain Authority: {backlink.domainAuthority}
										</p>
									</div>
								</Card>
							))}
						</div>
					</div>
				)}
			</div>
		</div>
	);
}