import { fal } from "@fal-ai/client";
import { auth } from "@/lib/auth";
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { videoGenerations } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

// Configure fal.ai client
fal.config({
  credentials: process.env.FAL_KEY,
});

export async function GET(req: NextRequest) {
  try {
    // Get user session
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the generation ID from the query parameters
    const url = new URL(req.url);
    const id = url.searchParams.get("id");

    if (!id) {
      return NextResponse.json(
        { error: "Generation ID is required" },
        { status: 400 }
      );
    }

    // Get the generation from the database
    const [generation] = await db
      .select()
      .from(videoGenerations)
      .where(eq(videoGenerations.id, id))
      .limit(1);

    if (!generation) {
      return NextResponse.json(
        { error: "Generation not found" },
        { status: 404 }
      );
    }

    // Check if the generation belongs to the current user
    if (generation.userId !== session.user.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // If the generation is already completed, return the result
    if (generation.status === "completed" && generation.videoUrl) {
      return NextResponse.json({
        id: generation.id,
        status: generation.status,
        videoUrl: generation.videoUrl,
        prompt: generation.prompt,
      });
    }

    // If there's no request ID, something went wrong
    if (!generation.requestId) {
      return NextResponse.json(
        {
          id: generation.id,
          status: "failed",
          error: "No request ID found",
        },
        { status: 200 }
      );
    }

    try {
      // Check the status from fal.ai
      const status = await fal.queue.status("fal-ai/ltx-video", {
        requestId: generation.requestId,
      });

      // If the generation is completed, update the database
      if (status.status === "COMPLETED") {
        try {
          const result = await fal.queue.result("fal-ai/ltx-video", {
            requestId: generation.requestId,
          });

          const videoUrl = result.data?.video?.url;

          if (videoUrl) {
            await db
              .update(videoGenerations)
              .set({
                status: "completed",
                videoUrl,
                updatedAt: new Date(),
              })
              .where(eq(videoGenerations.id, id));

            return NextResponse.json({
              id: generation.id,
              status: "completed",
              videoUrl,
              prompt: generation.prompt,
            });
          } else {
            throw new Error("No video URL in result");
          }
        } catch (resultError) {
          console.error("Error getting result:", resultError);
          await db
            .update(videoGenerations)
            .set({
              status: "failed",
              updatedAt: new Date(),
            })
            .where(eq(videoGenerations.id, id));

          return NextResponse.json(
            {
              id: generation.id,
              status: "failed",
              error: "Failed to get result",
            },
            { status: 200 }
          );
        }
      }

      // Handle failed status
      if (
        typeof status.status === "string" &&
        ["FAILED", "ERROR", "CANCELED"].includes(status.status)
      ) {
        await db
          .update(videoGenerations)
          .set({
            status: "failed",
            updatedAt: new Date(),
          })
          .where(eq(videoGenerations.id, id));

        return NextResponse.json({
          id: generation.id,
          status: "failed",
          error: "Generation failed",
        });
      }

      // If the status is not completed or failed, it's still in progress
      let queuePosition: number | undefined = undefined;

      if (status.status === "IN_QUEUE" && "position" in status) {
        queuePosition =
          typeof status.position === "number" ? status.position : undefined;
      }

      return NextResponse.json({
        id: generation.id,
        status: "pending",
        queueStatus: status.status,
        position: queuePosition,
        prompt: generation.prompt,
      });
    } catch (statusError) {
      console.error("Error checking status:", statusError);

      return NextResponse.json(
        {
          id: generation.id,
          status: generation.status,
          error: "Failed to check status",
        },
        { status: 200 }
      );
    }
  } catch (error) {
    console.error("Error checking video status:", error);

    return NextResponse.json(
      {
        error: "Failed to check video status",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 200 }
    );
  }
}
