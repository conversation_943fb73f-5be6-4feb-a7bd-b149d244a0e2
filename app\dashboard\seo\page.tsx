import {
  BarChart, Calculator,
  Clock,
  Code2,
  Cpu,
  FileCode,
  FileOutput,
  FileSearch,
  FileText,
  Gauge,
  Globe,
  Image as ImageIcon,
  Link2,
  Map,
  MonitorCheck,
  Network,
  Palette,
  Percent,
  RefreshCw,
  Scale,
  Search,
  Shield,
  Tags,
  TrendingUp,
  Type,
  Wifi,
  Youtube
} from "lucide-react";

import Link from "next/link";

const seoTools = [
  {
    title: "Article Rewriter",
    description: "Rewrite articles while maintaining context and improving quality",
    icon: FileText,
    href: "/dashboard/seo/article-rewriter",
  },
  {
    title: "Plagiarism Checker",
    description: "Check content for originality and detect potential plagiarism",
    icon: Search,
    href: "/dashboard/seo/plagiarism",
  },
  {
    title: "Backlink Maker",
    description: "Create high-quality backlinks to improve SEO",
    icon: Link2,
    href: "/dashboard/seo/backlink-maker",
  },
  {
    title: "Meta Tag Generator",
    description: "Generate optimized meta tags for better SEO",
    icon: Tags,
    href: "/dashboard/seo/meta-generator",
  },
  {
    title: "Meta Tags Analyzer",
    description: "Analyze and optimize your meta tags",
    icon: Bar<PERSON>hart,
    href: "/dashboard/seo/meta-analyzer",
  },
  {
    title: "Keyword Position Checker",
    description: "Track your keyword rankings in search results",
    icon: BarChart,
    href: "/dashboard/seo/keyword-position",
  },
  {
    title: "Robots.txt Generator",
    description: "Create and optimize your robots.txt file",
    icon: FileCode,
    href: "/dashboard/seo/robots-generator",
  },
  {
    title: "XML Sitemap Generator",
    description: "Generate XML sitemaps for better indexing",
    icon: Network,
    href: "/dashboard/seo/sitemap-generator",
  },
  {
    title: "Backlink Checker",
    description: "Analyze your backlink profile and quality",
    icon: Link2,
    href: "/dashboard/seo/backlink-checker",
  },
  {
    title: "Keyword CPC Calculator",
    description: "Calculate cost per click for your keywords",
    icon: Calculator,
    href: "/dashboard/seo/cpc-calculator",
  },
  {
    title: "Word Counter",
    description: "Count words, characters, and readability metrics",
    icon: Type,
    href: "/dashboard/seo/word-counter",
  },
  {
    title: "Online Ping Website Tool",
    description: "Ping websites and check their response time",
    icon: Globe,
    href: "/dashboard/seo/ping-tool",
  },
  {
    title: "Link Analyzer",
    description: "Analyze links for SEO and security issues",
    icon: Link2,
    href: "/dashboard/seo/link-analyzer",
  },
  {
    title: "Google Pagespeed Insights",
    description: "Check and optimize your website's performance",
    icon: Gauge,
    href: "/dashboard/seo/pagespeed",
  },
  {
    title: "My IP Address",
    description: "View your current IP address and location details",
    icon: Wifi,
    href: "/dashboard/seo/ip-address",
  },
  {
    title: "Keyword Density Checker",
    description: "Analyze keyword usage and density in your content",
    icon: Percent,
    href: "/dashboard/seo/keyword-density",
  },
  {
    title: "Google Malware Checker",
    description: "Scan your website for potential security threats",
    icon: Shield,
    href: "/dashboard/seo/malware-checker",
  },
  {
    title: "Domain Age Checker",
    description: "Check the age and registration details of domains",
    icon: Clock,
    href: "/dashboard/seo/domain-age",
  },
  {
    title: "Whois Checker",
    description: "Look up domain registration information",
    icon: FileSearch,
    href: "/dashboard/seo/whois",
  },
  {
    title: "Domain to IP",
    description: "Convert domain names to IP addresses",
    icon: Cpu,
    href: "/dashboard/seo/domain-ip",
  },
  {
    title: "URL Rewriting Tool",
    description: "Create and test URL rewriting rules",
    icon: RefreshCw,
    href: "/dashboard/seo/url-rewriter",
  },
  {
    title: "WWW Redirect Checker",
    description: "Test website redirects and configurations",
    icon: MonitorCheck,
    href: "/dashboard/seo/redirect-checker",
  },
  {
    title: "Mozrank Checker",
    description: "Check your website's Mozrank score",
    icon: TrendingUp,
    href: "/dashboard/seo/mozrank",
  },
  {
    title: "URL Encoder/Decoder",
    description: "Encode and decode URLs easily",
    icon: Code2,
    href: "/dashboard/seo/url-encoder",
  },
  {
    title: "Bulk GEO IP Locator",
    description: "Locate multiple IP addresses geographically",
    icon: Map,
    href: "/dashboard/seo/geo-ip",
  },
  {
    title: "Color Picker",
    description: "Pick and convert colors for web design",
    icon: Palette,
    href: "/dashboard/seo/color-picker",
  },
  {
    title: "Privacy Policy Generator",
    description: "Generate custom privacy policies",
    icon: FileOutput,
    href: "/dashboard/seo/privacy-policy",
  },
  {
    title: "Terms & Conditions Generator",
    description: "Create terms and conditions documents",
    icon: Scale,
    href: "/dashboard/seo/terms-conditions",
  },
  {
    title: "Image Placeholder Generator",
    description: "Generate placeholder images for development",
    icon: ImageIcon,
    href: "/dashboard/seo/placeholder-image",
  },
  {
    title: "YouTube Keywords Extractor",
    description: "Extract keywords from YouTube videos",
    icon: Youtube,
    href: "/dashboard/seo/youtube-keywords",
  },
];

export default function SEOToolsPage() {
  return (
    <div className="min-h-screen relative overflow-hidden py-6 sm:py-12">
      <div className="absolute inset-0 dashboard-gradient opacity-20" />
      <div className="relative px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
        <div className="flex flex-col gap-6">
          <div>
            <h1 className="text-4xl font-bold gradient-text mb-2">SEO Tools</h1>
            <p className="text-muted-foreground">
              Comprehensive tools to optimize your website&apos;s search engine performance
            </p>
          </div>

          <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
            {seoTools.map((tool) => (
              <Link key={tool.title} href={tool.href}>
                <div className="glass-card p-6 h-full hover:ring-2 hover:ring-primary/20 transition-all">
                  <div className="flex items-center gap-3 mb-4">
                    <tool.icon className="h-6 w-6 text-primary" />
                    <h2 className="text-xl font-semibold">{tool.title}</h2>
                  </div>
                  <p className="text-muted-foreground">{tool.description}</p>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
