import { GoogleGenerativeAI } from "@google/generative-ai";
import { NextResponse } from "next/server";

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);

export async function POST(req: Request) {
  try {
    const { url, keyword } = await req.json();

    const model = genAI.getGenerativeModel({ model: "gemini-2.5-pro" });

    const prompt = `Analyze the keyword position and provide SEO recommendations for the following website and keyword. Format the response as JSON with the following structure:
		{
			"position": number (1-100),
			"searchVolume": number,
			"difficulty": number (0-100),
			"recommendations": ["recommendation1", "recommendation2"]
		}

		Website URL: ${url}
		Keyword: ${keyword}

		Provide only the JSON response without any additional text.`;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    // Parse the response as JSON
    const jsonResult = JSON.parse(text);

    return NextResponse.json({ result: jsonResult });
  } catch (error) {
    console.error("Keyword position checker error:", error);
    return NextResponse.json(
      { error: "Failed to check keyword position" },
      { status: 500 }
    );
  }
}
