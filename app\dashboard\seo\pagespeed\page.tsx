"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Loader2 } from "lucide-react";
import { Card } from "@/components/ui/card";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";

interface PageSpeedResult {
	performance: number;
	accessibility: number;
	bestPractices: number;
	seo: number;
	metrics: {
		FCP: number;
		LCP: number;
		CLS: number;
		TTI: number;
	};
	suggestions: Array<{
		category: string;
		impact: string;
		description: string;
	}>;
}

export default function PageSpeedPage() {
	const [url, setUrl] = useState("");
	const [loading, setLoading] = useState(false);
	const [result, setResult] = useState<PageSpeedResult | null>(null);
	const { toast } = useToast();

	const handleAnalyze = async () => {
		if (!url.trim()) {
			toast({
				title: "Error",
				description: "Please enter a URL to analyze",
				variant: "destructive",
			});
			return;
		}

		try {
			setLoading(true);
			const response = await fetch("/api/seo/pagespeed", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ url }),
			});

			if (!response.ok) {
				throw new Error("Failed to analyze page speed");
			}

			const data = await response.json();
			setResult(data.result);
		} catch (err) {
            console.error("An error occurred:", err);
			toast({
				title: "Error",
				description: "Failed to analyze page speed. Please try again.",
				variant: "destructive",
			});
		} finally {
			setLoading(false);
		}
	};

	const getScoreColor = (score: number) => {
		if (score >= 90) return "text-green-600";
		if (score >= 50) return "text-yellow-600";
		return "text-red-600";
	};

	return (
		<div className="container mx-auto py-8">
			<h1 className="text-3xl font-bold mb-6">Google PageSpeed Insights</h1>
			<div className="grid gap-6">
				<div className="flex gap-4">
					<Input
						placeholder="Enter website URL"
						value={url}
						onChange={(e) => setUrl(e.target.value)}
						className="flex-1"
					/>
					<Button onClick={handleAnalyze} disabled={loading}>
						{loading ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								Analyzing...
							</>
						) : (
							"Analyze"
						)}
					</Button>
				</div>

				{result && (
					<div className="space-y-6">
						<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
							<Card className="p-4">
								<h3 className="text-sm font-medium">Performance</h3>
								<p className={`text-2xl font-bold ${getScoreColor(result.performance)}`}>
									{result.performance}
								</p>
							</Card>
							<Card className="p-4">
								<h3 className="text-sm font-medium">Accessibility</h3>
								<p className={`text-2xl font-bold ${getScoreColor(result.accessibility)}`}>
									{result.accessibility}
								</p>
							</Card>
							<Card className="p-4">
								<h3 className="text-sm font-medium">Best Practices</h3>
								<p className={`text-2xl font-bold ${getScoreColor(result.bestPractices)}`}>
									{result.bestPractices}
								</p>
							</Card>
							<Card className="p-4">
								<h3 className="text-sm font-medium">SEO</h3>
								<p className={`text-2xl font-bold ${getScoreColor(result.seo)}`}>
									{result.seo}
								</p>
							</Card>
						</div>

						<Card className="p-4">
							<h3 className="text-lg font-semibold mb-4">Core Web Vitals</h3>
							<Table>
								<TableHeader>
									<TableRow>
										<TableHead>Metric</TableHead>
										<TableHead>Value</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									<TableRow>
										<TableCell>First Contentful Paint (FCP)</TableCell>
										<TableCell>{result.metrics.FCP}s</TableCell>
									</TableRow>
									<TableRow>
										<TableCell>Largest Contentful Paint (LCP)</TableCell>
										<TableCell>{result.metrics.LCP}s</TableCell>
									</TableRow>
									<TableRow>
										<TableCell>Cumulative Layout Shift (CLS)</TableCell>
										<TableCell>{result.metrics.CLS}</TableCell>
									</TableRow>
									<TableRow>
										<TableCell>Time to Interactive (TTI)</TableCell>
										<TableCell>{result.metrics.TTI}s</TableCell>
									</TableRow>
								</TableBody>
							</Table>
						</Card>

						<Card className="p-4">
							<h3 className="text-lg font-semibold mb-4">Suggestions</h3>
							<div className="space-y-4">
								{result.suggestions.map((suggestion, index) => (
									<div key={index} className="p-4 border rounded-lg">
										<div className="flex justify-between items-center mb-2">
											<h4 className="font-medium">{suggestion.category}</h4>
											<span className={`px-2 py-1 rounded text-sm ${
												suggestion.impact === 'high' ? 'bg-red-100 text-red-800' :
												suggestion.impact === 'medium' ? 'bg-yellow-100 text-yellow-800' :
												'bg-green-100 text-green-800'
											}`}>
												{suggestion.impact} impact
											</span>
										</div>
										<p className="text-sm text-muted-foreground">
											{suggestion.description}
										</p>
									</div>
								))}
							</div>
						</Card>
					</div>
				)}
			</div>
		</div>
	);
}