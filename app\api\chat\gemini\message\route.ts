import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { messages, chats } from "@/lib/db/schema";
import { GoogleGenerativeAI } from "@google/generative-ai";
import { eq } from "drizzle-orm";
import { nanoid } from "nanoid";

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);

export async function POST(req: Request) {
  try {
    const session = await auth();
    if (!session?.user) {
      return new Response(JSON.stringify({ error: "Unauthorized" }), {
        status: 401,
      });
    }

    const { messages: messageHistory, chatId, model } = await req.json();
    const lastMessage = messageHistory[messageHistory.length - 1];
    const messageId = nanoid();
    const userMessageId = nanoid();

    // IMPORTANT: First check and create chat before any message operations
    const existingChat = await db.query.chats.findFirst({
      where: eq(chats.id, chatId),
    });

    if (!existingChat) {
      // Create chat first
      await db.insert(chats).values({
      id: chatId,
      userId: session.user.id,
      model: model || "gemini-1.5-pro",
      title: lastMessage.content.slice(0, 100),
      createdAt: new Date(),
      });
    }

    // Now safe to store the user's message as chat exists
    await db.insert(messages).values({
      id: userMessageId,
      chatId,
      role: "user",
      content: lastMessage.content,
      createdAt: new Date(),
    });

    // Format history for Gemini API - ensure proper role mapping
    const formattedHistory = messageHistory
      .slice(0, -1)
      .map((msg: { role: string; content: string }) => ({
      role: msg.role === "user" ? "user" : "model",
      parts: [{ text: msg.content }],
      }));

    // Initialize chat with the current message
    const genModel = genAI.getGenerativeModel({
      model: model || "gemini-1.5-pro",
    });
    const chat = genModel.startChat({
      history: formattedHistory,
      generationConfig: {
        maxOutputTokens: 10000,
      },
    });

    try {
      // Send the message with proper formatting
      const result = await chat.sendMessageStream([
        { text: lastMessage.content },
      ]);

      // Create initial database entry
      await db.insert(messages).values({
        id: messageId,
        chatId,
        role: "assistant",
        content: "",
        createdAt: new Date(),
      });

      // Set up streaming response
      const encoder = new TextEncoder();
      let fullResponse = "";

      const stream = new ReadableStream({
        async start(controller) {
          try {
            for await (const chunk of result.stream) {
              const text = chunk.text();
              fullResponse += text;

              const chunkData = {
                id: messageId,
                role: "assistant",
                content: text,
              };

              controller.enqueue(
                encoder.encode(`data: ${JSON.stringify(chunkData)}\n\n`)
              );
            }

            // Update final content in database
            await db
              .update(messages)
              .set({ content: fullResponse })
              .where(eq(messages.id, messageId));

            controller.enqueue(encoder.encode("data: [DONE]\n\n"));
            controller.close();
          } catch (error) {
            console.error("Stream processing error:", error);
            controller.error(error);
          }
        },
      });

      return new Response(stream, {
        headers: {
          "Content-Type": "text/event-stream",
          "Cache-Control": "no-cache",
          Connection: "keep-alive",
        },
      });
    } catch (error) {
      console.error("Gemini API error:", error);
      return new Response(
        JSON.stringify({
          error:
            error instanceof Error
              ? error.message
              : "Failed to process message",
        }),
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("[CHAT_ERROR]", error);
    return new Response(
      JSON.stringify({
        error: error instanceof Error ? error.message : "Internal server error",
      }),
      { status: 500 }
    );
  }
}
