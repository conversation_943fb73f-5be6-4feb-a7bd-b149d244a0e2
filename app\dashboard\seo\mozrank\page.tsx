"use client";

import { useState } from "react";
import { TrendingUp } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";

export default function MozrankPage() {
	const [url, setUrl] = useState("");
	const [loading, setLoading] = useState(false);
	const [result, setResult] = useState<{ mozrank: number; domain_authority: number } | null>(null);
	const [error, setError] = useState("");

	const checkMozrank = async () => {
		setLoading(true);
		setError("");
		try {
			const response = await fetch("/api/mozrank", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({ url }),
			});
			
			const data = await response.json();
			if (!response.ok) throw new Error(data.error);
			setResult(data);
		} catch (err) {
			setError(err instanceof Error ? err.message : "Failed to check Mozrank");
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="container mx-auto py-8">
			<div className="flex flex-col gap-6">
				<div>
					<h1 className="text-4xl font-bold gradient-text mb-2">Mozrank Checker</h1>
					<p className="text-muted-foreground">
						Check your website&aps;s Mozrank score and domain authority.
					</p>
				</div>

				<Card className="p-6">
					<div className="flex flex-col gap-4">
						<div>
							<label className="block text-sm font-medium mb-2">Website URL</label>
							<div className="flex gap-2">
								<Input
									placeholder="Enter URL (e.g., https://example.com)"
									value={url}
									onChange={(e) => setUrl(e.target.value)}
								/>
								<Button onClick={checkMozrank} disabled={loading}>
									<TrendingUp className="mr-2 h-4 w-4" />
									{loading ? "Checking..." : "Check Mozrank"}
								</Button>
							</div>
						</div>

						{error && (
							<Alert variant="destructive">
								<AlertDescription>{error}</AlertDescription>
							</Alert>
						)}

						{result && (
							<div className="grid grid-cols-2 gap-4">
								<Card className="p-4">
									<h3 className="text-lg font-medium mb-2">Mozrank</h3>
									<p className="text-3xl font-bold">{result.mozrank.toFixed(2)}</p>
								</Card>
								<Card className="p-4">
									<h3 className="text-lg font-medium mb-2">Domain Authority</h3>
									<p className="text-3xl font-bold">{result.domain_authority}</p>
								</Card>
							</div>
						)}
					</div>
				</Card>
			</div>
		</div>
	);
}