import { GoogleGenerativeAI } from "@google/generative-ai";
import { NextResponse } from "next/server";

// Use the correct environment variable name
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);

function cleanJsonResponse(text: string): string {
	text = text.replace(/```json\n/g, '').replace(/```/g, '');
	return text.trim();
}

export async function POST(req: Request) {
	try {
		// Verify API key is present
		if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
			throw new Error("Google Generative AI API key is not configured");
		}

		const { keyword } = await req.json();
		
		const model = genAI.getGenerativeModel({ model: "gemini-pro" });
		
		try {
			const result = await model.generateContent(`Analyze the following keyword and provide CPC metrics. Return the response in this exact JSON format without any markdown or additional text:
			{
				"keyword": "${keyword}",
				"estimatedCPC": number,
				"monthlySearches": number,
				"competition": number,
				"suggestedBid": number
			}`);

			const response = await result.response;
			const text = response.text();
			
			const cleanedText = cleanJsonResponse(text);
			const jsonResult = JSON.parse(cleanedText);

			return NextResponse.json({ result: jsonResult });
		} catch (apiError) {
			console.error("API call failed:", apiError);
			return NextResponse.json(
				{ error: "Failed to connect to AI service. Please try again later." },
				{ status: 503 }
			);
		}
	} catch (error) {
		console.error("CPC calculator error:", error);
		if (error instanceof Error && error.message.includes("API key")) {
			return NextResponse.json(
				{ error: "Service configuration error. Please contact support." },
				{ status: 500 }
			);
		}
		return NextResponse.json(
			{ error: "Failed to calculate CPC" },
			{ status: 500 }
		);
	}
}