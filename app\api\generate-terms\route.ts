import { NextResponse } from "next/server";

interface TermsFormData {
	companyName: string;
	website: string;
	email: string;
	hasUserAccounts: boolean;
	hasPayments: boolean;
	hasUserContent: boolean;
}

export async function POST(request: Request) {
	try {
		const formData: TermsFormData = await request.json();

		const terms = generateTerms(formData);
		return NextResponse.json({ terms });
	} catch (error) {
        console.log(error);
		return NextResponse.json(
			{ error: "Failed to generate terms and conditions" },
			{ status: 500 }
		);
	}
}

function generateTerms(data: TermsFormData): string {
	const currentDate = new Date().toLocaleDateString();
	
	let terms = `Terms and Conditions for ${data.companyName}

Last updated: ${currentDate}

1. Introduction
Welcome to ${data.website}. By accessing this website, you agree to be bound by these Terms and Conditions.

2. Contact Information
For any questions regarding these terms, please contact us at:
Email: ${data.email}
Website: ${data.website}

`;

	if (data.hasUserAccounts) {
		terms += `3. User Accounts
Users are responsible for maintaining the confidentiality of their account information and password.
Users must be at least 18 years old to create an account.

`;
	}

	if (data.hasPayments) {
		terms += `4. Payments and Refunds
All payments are processed securely through our payment providers.
Refunds are handled according to our refund policy.

`;
	}

	if (data.hasUserContent) {
		terms += `5. User Content
Users retain their rights to any content they submit, post, or display on the website.
We reserve the right to remove any content that violates these terms.

`;
	}

	terms += `6. Limitation of Liability
${data.companyName} shall not be liable for any indirect, incidental, special, consequential, or punitive damages.

7. Changes to Terms
We reserve the right to modify these terms at any time. Users will be notified of any changes.

8. Governing Law
These terms shall be governed by and construed in accordance with the laws of the jurisdiction in which ${data.companyName} operates.`;

	return terms;
}