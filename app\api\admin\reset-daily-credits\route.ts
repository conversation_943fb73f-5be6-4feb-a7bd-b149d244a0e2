import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { sql } from "drizzle-orm";
import { auth } from "@/lib/auth";

export async function POST() {
  try {
    // Check if user is authenticated and is admin
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized - Please log in" },
        { status: 401 }
      );
    }

    // Get user details to check if admin
    const user = await db.query.users.findFirst({
      where: (users, { eq }) => eq(users.id, session.user.id),
    });

    if (!user || user.userType !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized - Admin access required" },
        { status: 403 }
      );
    }

    // Call the PostgreSQL function to reset daily credits
    const result = await db.execute(
      sql`SELECT * FROM manual_reset_daily_credits()`
    );

    const resetResult = result[0] as {
      users_affected: number;
      credits_assigned: number;
      reset_id: string;
      success: boolean;
      error_message: string | null;
    };

    if (!resetResult.success) {
      return NextResponse.json(
        {
          error: "Failed to reset daily credits",
          message: resetResult.error_message || "Unknown database error",
          resetId: resetResult.reset_id,
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Daily credits reset completed successfully`,
      usersAffected: resetResult.users_affected,
      creditsAssigned: resetResult.credits_assigned,
      resetId: resetResult.reset_id,
      triggeredBy: user.email,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Manual daily credit reset failed:", error);
    return NextResponse.json(
      {
        error: "Failed to reset daily credits",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// GET endpoint to check the status of recent resets
export async function GET() {
  try {
    // Check if user is authenticated and is admin
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized - Please log in" },
        { status: 401 }
      );
    }

    // Get user details to check if admin
    const user = await db.query.users.findFirst({
      where: (users, { eq }) => eq(users.id, session.user.id),
    });

    if (!user || user.userType !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized - Admin access required" },
        { status: 403 }
      );
    }

    // Get the last 10 reset operations
    const recentResets = await db.query.dailyCreditResets.findMany({
      orderBy: (dailyCreditResets, { desc }) => [
        desc(dailyCreditResets.createdAt),
      ],
      limit: 10,
    });

    return NextResponse.json({
      success: true,
      recentResets,
      requestedBy: user.email,
    });
  } catch (error) {
    console.error("Failed to fetch reset status:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch reset status",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
