import { NextRequest, NextResponse } from "next/server";

export async function GET(req: NextRequest) {
  try {
    // Verify the request is from a cron service
    const authHeader = req.headers.get("authorization");
    const cronSecret = process.env.CRON_SECRET;
    
    if (!cronSecret) {
      console.error("CRON_SECRET environment variable is not set");
      return NextResponse.json(
        { error: "Server configuration error" },
        { status: 500 }
      );
    }

    if (authHeader !== `Bearer ${cronSecret}`) {
      console.error("Unauthorized cron request");
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Call the admin reset endpoint
    const baseUrl = process.env.NEXTAUTH_URL || "http://localhost:3000";
    const resetResponse = await fetch(`${baseUrl}/api/admin/reset-daily-credits`, {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${cronSecret}`,
        "Content-Type": "application/json",
      },
    });

    if (!resetResponse.ok) {
      const errorData = await resetResponse.json();
      throw new Error(`Reset failed: ${errorData.message || errorData.error}`);
    }

    const resetData = await resetResponse.json();

    return NextResponse.json({
      success: true,
      message: "Daily credit reset cron job completed successfully",
      timestamp: new Date().toISOString(),
      ...resetData,
    });

  } catch (error) {
    console.error("Cron job failed:", error);
    return NextResponse.json(
      {
        error: "Cron job failed",
        message: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Also support POST for flexibility
export async function POST(req: NextRequest) {
  return GET(req);
}
