import nodemailer from 'nodemailer';

// Create a transporter using Gmail SMTP
const transporter = nodemailer.createTransport({
	host: "smtp.gmail.com",
	port: 465,
  	service: "gmail",
  	secure: true,
	auth: {
	  user: process.env.EMAIL,
	  pass: process.env.PASSWORD,
	},
  });

export async function sendEmail({
	to,
	subject,
	html
}: {
	to: string;
	subject: string;
	html: string;
}) {
	try {
		const mailOptions = {
			from: process.env.GMAIL_USER,
			to,
			subject,
			html,
		};

		await transporter.sendMail(mailOptions);
		return { success: true };
	} catch (error) {
		console.error('Error sending email:', error);
		return { success: false, error: 'Failed to send email' };
	}
}