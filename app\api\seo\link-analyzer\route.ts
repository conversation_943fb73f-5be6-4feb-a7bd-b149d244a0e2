import { GoogleGenerativeAI } from "@google/generative-ai";
import { NextResponse } from "next/server";

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);

function cleanJsonResponse(text: string): string {
	text = text.replace(/```json\n/g, '').replace(/```/g, '');
	return text.trim();
}

export async function POST(req: Request) {
	try {
		const { url } = await req.json();
		
		const model = genAI.getGenerativeModel({ model: "gemini-pro" });
		
		const prompt = `Analyze the following URL and provide detailed information about its SEO and security aspects. Return the response in this exact JSON format without any markdown formatting or additional text:
		{
			"url": "input url",
			"status": number (HTTP status code),
			"isSecure": boolean (HTTPS check),
			"loadTime": number (estimated load time in ms),
			"seoIssues": ["issue1", "issue2"],
			"securityIssues": ["issue1", "issue2"],
			"redirectChain": ["url1", "url2"]
		}

		URL: ${url}

		Provide realistic analysis based on typical web standards and best practices.`;

		const result = await model.generateContent(prompt);
		const response = await result.response;
		const text = response.text();
		
		const cleanedText = cleanJsonResponse(text);
		const jsonResult = JSON.parse(cleanedText);

		return NextResponse.json({ result: jsonResult });
	} catch (error) {
		console.error("Link analyzer error:", error);
		return NextResponse.json(
			{ error: "Failed to analyze link" },
			{ status: 500 }
		);
	}
}