"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card } from "@/components/ui/card";

export default function WordCounterPage() {
	const [text, setText] = useState("");
	const [stats, setStats] = useState({
		words: 0,
		characters: 0,
		sentences: 0,
		paragraphs: 0,
		readingTime: 0,
	});

	useEffect(() => {
		const words = text.trim().split(/\s+/).filter(word => word.length > 0).length;
		const characters = text.length;
		const sentences = text.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0).length;
		const paragraphs = text.split(/\n\s*\n/).filter(para => para.trim().length > 0).length;
		const readingTime = Math.ceil(words / 200); // Average reading speed of 200 words per minute

		setStats({
			words,
			characters,
			sentences,
			paragraphs,
			readingTime,
		});
	}, [text]);

	const handleClear = () => {
		setText("");
	};

	return (
		<div className="container mx-auto py-8">
			<h1 className="text-3xl font-bold mb-6">Word Counter</h1>
			<div className="grid gap-6">
				<Textarea
					placeholder="Type or paste your text here..."
					value={text}
					onChange={(e) => setText(e.target.value)}
					rows={12}
					className="w-full"
				/>
				<Button 
					onClick={handleClear}
					variant="outline"
					className="w-full md:w-auto"
				>
					Clear Text
				</Button>
				<div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
					<Card className="p-4">
						<h3 className="text-lg font-semibold">Words</h3>
						<p className="text-2xl">{stats.words}</p>
					</Card>
					<Card className="p-4">
						<h3 className="text-lg font-semibold">Characters</h3>
						<p className="text-2xl">{stats.characters}</p>
					</Card>
					<Card className="p-4">
						<h3 className="text-lg font-semibold">Sentences</h3>
						<p className="text-2xl">{stats.sentences}</p>
					</Card>
					<Card className="p-4">
						<h3 className="text-lg font-semibold">Paragraphs</h3>
						<p className="text-2xl">{stats.paragraphs}</p>
					</Card>
					<Card className="p-4">
						<h3 className="text-lg font-semibold">Reading Time</h3>
						<p className="text-2xl">{stats.readingTime} min</p>
					</Card>
				</div>
			</div>
		</div>
	);
}