import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { chats, messages } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

export async function DELETE(req: Request) {
	try {
		const session = await auth();
		if (!session?.user) {
			return new Response("Unauthorized", { status: 401 });
		}

		const { chatId } = await req.json();

		// Delete all messages associated with the chat
		await db.delete(messages).where(eq(messages.chatId, chatId));
		
		// Delete the chat
		await db.delete(chats).where(eq(chats.id, chatId));

		return new Response(JSON.stringify({ success: true }), {
			status: 200,
		});
	} catch (error) {
		console.error("[DELETE_CHAT_ERROR]", error);
		return new Response(
			JSON.stringify({
				error: error instanceof Error ? error.message : "Internal server error",
			}),
			{ status: 500 }
		);
	}
}