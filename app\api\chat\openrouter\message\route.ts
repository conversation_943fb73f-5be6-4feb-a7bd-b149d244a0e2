import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { chats, messages } from "@/lib/db/schema";
import { nanoid } from "nanoid";
import OpenAI from "openai";
import { eq } from "drizzle-orm";

interface ChatMessage {
  role: "user" | "assistant";
  content: string;
}

const openai = new OpenAI({
  baseURL: "https://openrouter.ai/api/v1",
  apiKey: process.env.OPENROUTER_API_KEY,
  defaultHeaders: {
    "HTTP-Referer": process.env.NEXTAUTH_URL,
    "X-Title": "AI Vora",
  },
});

export async function POST(req: Request) {
  try {
    const session = await auth();
    if (!session?.user) {
      return new Response(JSON.stringify({ error: "Unauthorized" }), {
        status: 401,
      });
    }

    const { messages: chatMessages, chatId, model } = await req.json();

    // Validate chatId
    if (!chatId) {
      return new Response(JSON.stringify({ error: "Chat ID is required" }), {
        status: 400,
      });
    }

    // Check if chat exists and create if needed
    let existingChat = await db.query.chats.findFirst({
      where: eq(chats.id, chatId),
    });

    if (!existingChat) {
      const [newChat] = await db
        .insert(chats)
        .values({
          id: chatId,
          userId: session.user.id,
          model: model || "openai/gpt-3.5-turbo",
          title: chatMessages[0]?.content.substring(0, 100) || "New Chat",
          createdAt: new Date(),
        })
        .returning();
      existingChat = newChat;
    }

    const messageId = nanoid();
    const userMessageId = nanoid();

    // Store user message
    await db.insert(messages).values({
      id: userMessageId,
      chatId,
      role: "user",
      content: chatMessages[chatMessages.length - 1].content,
      createdAt: new Date(),
    });

    // Create initial assistant message
    await db.insert(messages).values({
      id: messageId,
      chatId,
      role: "assistant",
      content: "",
      createdAt: new Date(),
    });

    // Determine the OpenRouter model based on the requested model
    let openRouterModel = "openai/gpt-oss-20b:free";
    switch (model) {
      case "gpt5":
        openRouterModel = "openai/gpt-oss-20b:free";
        break;
      case "gpt4":
        openRouterModel = "openai/gpt-oss-20b:free";
        break;
      case "grok":
        openRouterModel = "openai/gpt-oss-20b:free";
        break;
      case "claude35sonnet":
        openRouterModel = "openai/gpt-oss-20b:free";
        break;
      case "googlegemini":
        openRouterModel = "google/gemini-2.0-flash-exp:free";
        break;
      case "deepseek":
        openRouterModel = "deepseek/deepseek-r1-0528:free";
        break;
      case "llama33":
        openRouterModel = "meta-llama/llama-3.3-70b-instruct:free";
        break;
      default:
        openRouterModel = "openai/gpt-oss-20b:free";
    }

    let completion;
    try {
      completion = await openai.chat.completions.create({
        model: openRouterModel,
        messages: chatMessages.map((msg: ChatMessage) => ({
          role: msg.role,
          content: msg.content,
        })),
        stream: true,
        max_tokens: 4000,
        temperature: 0.7,
      });
    } catch (apiError) {
      console.error("OpenRouter API error:", apiError);

      // Update message with error content
      await db
        .update(messages)
        .set({
          content:
            "I apologize, but the AI service is currently unavailable. Please try again later or use a different model.",
        })
        .where(eq(messages.id, messageId));

      return new Response(
        JSON.stringify({
          error: "AI service temporarily unavailable",
          details: "Please try again later or use a different model",
        }),
        {
          status: 503,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    let fullResponse = "";
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
      async start(controller) {
        try {
          for await (const chunk of completion) {
            try {
              const text = chunk.choices[0]?.delta?.content || "";
              if (text) {
                fullResponse += text;

                const chunkData = {
                  id: messageId,
                  role: "assistant",
                  content: text,
                };

                controller.enqueue(
                  encoder.encode(`data: ${JSON.stringify(chunkData)}\n\n`)
                );
              }
            } catch (chunkError) {
              console.error("Chunk processing error:", chunkError);
              // Continue processing other chunks
              continue;
            }
          }

          // Update final content in database
          if (fullResponse) {
            await db
              .update(messages)
              .set({ content: fullResponse })
              .where(eq(messages.id, messageId));
          }

          controller.enqueue(encoder.encode("data: [DONE]\n\n"));
        } catch (error) {
          console.error("Stream processing error:", error);

          // Send error message to client
          const errorMessage = {
            id: messageId,
            role: "assistant",
            content:
              "I apologize, but I'm experiencing technical difficulties. Please try again in a moment.",
            error: true,
          };

          try {
            controller.enqueue(
              encoder.encode(`data: ${JSON.stringify(errorMessage)}\n\n`)
            );
            controller.enqueue(encoder.encode("data: [DONE]\n\n"));
          } catch (controllerError) {
            console.error("Controller error:", controllerError);
          }

          // Save error message to database
          try {
            await db
              .update(messages)
              .set({
                content:
                  "I apologize, but I'm experiencing technical difficulties. Please try again in a moment.",
              })
              .where(eq(messages.id, messageId));
          } catch (dbError) {
            console.error("Database error:", dbError);
          }
        } finally {
          try {
            controller.close();
          } catch (closeError) {
            console.error("Controller close error:", closeError);
          }
        }
      },
    });

    return new Response(stream, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache, no-transform",
        Connection: "keep-alive",
        "X-Accel-Buffering": "no",
        "Transfer-Encoding": "chunked",
        ...(chatId && { "X-Chat-Id": chatId }),
      },
    });
  } catch (error) {
    console.error("[OPENROUTER_CHAT_ERROR]", error);
    return new Response(
      JSON.stringify({
        error: error instanceof Error ? error.message : "Internal server error",
      }),
      { status: 500 }
    );
  }
}
