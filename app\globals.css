@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /* Hide scrollbar for Chrome, Safari and Opera */
  ::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  * {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
    @apply border-border;
  }

  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
  }
}

@layer base {
  body {
    @apply bg-background text-foreground;
  }
}

.auth-illustration {
  -webkit-mask-image: linear-gradient(to bottom, black, transparent);
  mask-image: linear-gradient(to bottom, black, transparent);
}

.auth-gradient {
  background: radial-gradient(
    circle at center,
    hsl(var(--primary) / 0.1),
    transparent 70%
  );
}

.glass-effect {
  @apply bg-white/10 backdrop-blur-lg border border-white/20;
}

.input-glass {
  @apply glass-effect px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-white/50;
}

.button-glass {
  @apply glass-effect px-6 py-2 rounded-md hover:bg-white/20 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white/50;
}

.gradient-text {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500;
}

@layer components {
  .glass-card {
    @apply backdrop-blur-xl bg-white/10 dark:bg-black/10 border border-white/20 dark:border-white/10 rounded-xl shadow-xl;
  }

  .gradient-text {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/80;
  }

  .auth-gradient {
    background: radial-gradient(
        circle at top center,
        hsl(var(--primary)) 0%,
        transparent 70%
      ),
      radial-gradient(
        circle at bottom left,
        hsl(var(--destructive)) 0%,
        transparent 70%
      ),
      radial-gradient(
        circle at bottom right,
        hsl(var(--primary)) 0%,
        transparent 70%
      );
  }

  .dashboard-gradient {
    background: radial-gradient(
        circle at top right,
        hsl(var(--primary)) 0%,
        transparent 70%
      ),
      radial-gradient(
        circle at center left,
        hsl(var(--destructive)) 0%,
        transparent 70%
      ),
      radial-gradient(
        circle at bottom center,
        hsl(var(--primary)) 0%,
        transparent 70%
      );
  }

  /* Custom scrollable content class */
  .scrollable-content {
    @apply overflow-y-auto;
    scroll-behavior: smooth;
  }

  /* Sidebar scrollbar styling */
  .sidebar-scroll {
    @apply overflow-y-auto;
    scroll-behavior: smooth;
    scrollbar-width: thin;
    scrollbar-color: rgba(6, 182, 212, 0.3) rgba(0, 0, 0, 0.1);
  }

  /* Webkit scrollbar for sidebar */
  .sidebar-scroll::-webkit-scrollbar {
    width: 6px;
    display: block;
    transition: width 0.2s ease;
  }

  /* Make scrollbar more visible on hover */
  .sidebar-scroll:hover::-webkit-scrollbar {
    width: 8px;
  }

  .sidebar-scroll::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
    margin: 8px 0;
  }

  .sidebar-scroll::-webkit-scrollbar-thumb {
    background: linear-gradient(
      to bottom,
      rgba(6, 182, 212, 0.4),
      rgba(139, 92, 246, 0.4)
    );
    border-radius: 3px;
    transition: all 0.2s ease;
  }

  .sidebar-scroll::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(
      to bottom,
      rgba(6, 182, 212, 0.6),
      rgba(139, 92, 246, 0.6)
    );
  }

  .sidebar-scroll::-webkit-scrollbar-thumb:active {
    background: linear-gradient(
      to bottom,
      rgba(6, 182, 212, 0.8),
      rgba(139, 92, 246, 0.8)
    );
  }

  /* Scrollbar corner */
  .sidebar-scroll::-webkit-scrollbar-corner {
    background: transparent;
  }

  /* Scroll fade indicator */
  .scroll-fade-bottom {
    position: relative;
  }

  .scroll-fade-bottom::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 20px;
    background: linear-gradient(to bottom, transparent, rgba(15, 23, 42, 0.8));
    pointer-events: none;
    z-index: 10;
  }

  .dark .scroll-fade-bottom::after {
    background: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.8));
  }
}

/* Auth page specific styles */
.auth-gradient {
  background: radial-gradient(
    circle at center,
    hsl(var(--primary) / 0.1),
    transparent 70%
  );
}

.gradient-text {
  background: linear-gradient(
    to right,
    hsl(var(--primary)),
    hsl(var(--primary) / 0.8)
  );
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.glass-card {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  background: linear-gradient(
    to bottom right,
    hsl(var(--background) / 0.8),
    hsl(var(--background) / 0.4)
  );
  border: 1px solid hsl(var(--primary) / 0.1);
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1),
    inset 0 1px 0 hsl(var(--primary) / 0.05);
}

.input-glass {
  background: linear-gradient(
    to bottom right,
    hsl(var(--background) / 0.8),
    hsl(var(--background) / 0.4)
  );
  border: 1px solid hsl(var(--primary) / 0.1);
  transition: all 0.2s ease-in-out;
}

.input-glass:focus {
  border-color: hsl(var(--primary));
  box-shadow: 0 0 0 2px hsl(var(--primary) / 0.1);
  outline: none;
}

.button-glass {
  background: linear-gradient(
    to bottom right,
    hsl(var(--primary)),
    hsl(var(--primary) / 0.9)
  );
  border: 1px solid hsl(var(--primary) / 0.1);
  transition: all 0.2s ease-in-out;
}

.button-glass:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px hsl(var(--primary) / 0.2);
}

.button-glass:active:not(:disabled) {
  transform: translateY(0);
}
