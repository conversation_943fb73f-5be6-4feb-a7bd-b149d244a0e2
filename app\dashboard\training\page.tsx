"use client";

import { Crown } from "lucide-react";
import Link from "next/link";

export default function TrainingPage() {
  return (
    <div className="min-h-screen relative overflow-hidden py-6 sm:py-12">
      <div className="absolute inset-0 dashboard-gradient opacity-20" />
      <div className="relative px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
        <div className="flex flex-col gap-6">
          <div>
            <h1 className="text-4xl font-bold gradient-text mb-2">Free Training Access</h1>
            <p className="text-muted-foreground">
              Access your exclusive DeepSeek AI training materials
            </p>
          </div>

            {/* Banner Video Section */}
            <div className="glass-card p-6">
            <div className="relative w-full" style={{ paddingBottom: '56.25%' }}> {/* 16:9 Aspect Ratio */}
              <video 
              className="absolute top-0 left-0 w-full h-full rounded-lg"
              autoPlay 
              loop 
              muted 
              playsInline
              >
              <source src="/banner.mp4" type="video/mp4" />
              Your browser does not support the video tag.
              </video>
            </div>
            </div>

            {/* Training Link Card */}
            <Link 
            href="https://jvz7.com/c/1467033/406225"
            target="_blank"
            rel="noopener noreferrer"

          >
            <div className="glass-card p-6 hover:ring-2 hover:ring-primary/20 transition-all border-2 border-primary/50 bg-primary/5">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <Crown className="h-6 w-6 text-yellow-500" />
                  <h2 className="text-xl font-semibold">Access Your Free Training</h2>
                </div>
                <span className="text-xs font-medium px-2 py-0.5 rounded bg-yellow-500/20 text-yellow-500">
                  Exclusive
                </span>
              </div>
              <p className="text-muted-foreground mb-4">
                Click here to access your exclusive DeepSeek AI training materials and start learning today.
              </p>
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
}

