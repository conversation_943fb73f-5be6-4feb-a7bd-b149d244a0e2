"use client";

import { useState } from "react";
import { Search } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";

interface KeywordResult {
	keyword: string;
	relevance: number;
}

export default function YouTubeKeywordsPage() {
	const [url, setUrl] = useState("");
	const [loading, setLoading] = useState(false);
	const [keywords, setKeywords] = useState<KeywordResult[]>([]);
	const [error, setError] = useState("");

	const extractKeywords = async () => {
		if (!url) return;

		setLoading(true);
		setError("");
		setKeywords([]);

		try {
			const response = await fetch("/api/youtube-keywords", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({ url }),
			});

			const data = await response.json();
			if (!response.ok) throw new Error(data.error);
			
			setKeywords(data.keywords);
		} catch (err) {
			setError(err instanceof Error ? err.message : "Failed to extract keywords");
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="container mx-auto py-8">
			<div className="flex flex-col gap-6">
				<div>
					<h1 className="text-4xl font-bold gradient-text mb-2">YouTube Keywords Extractor</h1>
					<p className="text-muted-foreground">
						Extract keywords and tags from YouTube videos
					</p>
				</div>

				<Card className="p-6">
					<div className="flex flex-col gap-4">
						<div>
							<label className="block text-sm font-medium mb-2">YouTube Video URL</label>
							<div className="flex gap-2">
								<Input
									placeholder="https://www.youtube.com/watch?v=..."
									value={url}
									onChange={(e) => setUrl(e.target.value)}
								/>
								<Button onClick={extractKeywords} disabled={loading || !url}>
									<Search className="mr-2 h-4 w-4" />
									{loading ? "Extracting..." : "Extract Keywords"}
								</Button>
							</div>
						</div>

						{error && (
							<Alert variant="destructive">
								<AlertDescription>{error}</AlertDescription>
							</Alert>
						)}

						{keywords.length > 0 && (
							<div className="mt-4">
								<h3 className="font-medium mb-4">Extracted Keywords:</h3>
								<div className="flex flex-wrap gap-2">
									{keywords.map((keyword, index) => (
										<Badge
											key={index}
											variant="secondary"
											className="text-sm"
											style={{
												opacity: Math.max(0.5, keyword.relevance),
											}}
										>
											{keyword.keyword}
										</Badge>
									))}
								</div>
							</div>
						)}
					</div>
				</Card>
			</div>
		</div>
	);
}