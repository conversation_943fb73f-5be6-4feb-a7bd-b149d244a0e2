"use client";

import {
  Image,
  MessageSquare,
  /*Search,*/ Sparkles,
  Video,
} from "lucide-react";
import Link from "next/link";

// const stats = [
//   {
//     title: "AI Conversations",
//     value: "0",
//     description: "Active chats across all AI models",
//     icon: MessageSquare,
//   },
//   {
//     title: "Images Created",
//     value: "0",
//     description: "AI-generated images",
//     icon: Image,
//   },
//   {
//     title: "Videos Generated",
//     value: "0",
//     description: "AI-powered videos",
//     icon: Video,
//   },
//   {
//     title: "SEO Analyses",
//     value: "0",
//     description: "Content optimizations",
//     icon: Search,
//   },
// ];

const quickActions = [
  {
    title: "Start New Chat",
    description: "Chat with DeepSeek, OpenAI, Google Gemini, or Ollama",
    href: "/dashboard/chat",
    icon: MessageSquare,
  },
  {
    title: "Generate Image",
    description: "Create images with AI models",
    href: "/dashboard/image",
    icon: Image,
  },
  {
    title: "Create Video",
    description: "Transform ideas into video content",
    href: "/dashboard/video",
    icon: Video,
  },
];

export default function DashboardPage() {
  return (
    <div className="min-h-screen relative overflow-hidden py-6 sm:py-12">
      <div className="relative px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto space-y-8">
        <div className="flex items-center justify-between">
          <div className="relative">
            {/* Tech frame for title */}
            <div className="absolute -inset-2 rounded-xl border border-cyan-400/30 bg-gradient-to-r from-cyan-500/10 via-purple-500/10 to-pink-500/10 backdrop-blur-sm" />
            <div className="absolute top-0 left-0 w-3 h-3 border-t-2 border-l-2 border-cyan-400 rounded-tl-xl" />
            <div className="absolute bottom-0 right-0 w-3 h-3 border-b-2 border-r-2 border-pink-400 rounded-br-xl" />
            <h2 className="relative text-3xl font-bold bg-gradient-to-r from-cyan-300 via-purple-300 to-pink-300 bg-clip-text text-transparent drop-shadow-lg px-4 py-2">
              Dashboard
            </h2>
          </div>
        </div>

        {/* Stats Grid */}
        {/* <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
          {stats.map((stat) => (
            <div key={stat.title} className="glass-card p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-medium">{stat.title}</h3>
                <stat.icon className="h-5 w-5 text-primary" />
              </div>
              <div className="text-2xl font-bold gradient-text mb-1">{stat.value}</div>
              <p className="text-xs text-muted-foreground">{stat.description}</p>
            </div>
          ))}
        </div> */}

        {/* Quick Actions */}
        <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
          {quickActions.map((action) => (
            <Link key={action.title} href={action.href}>
              <div className="group relative p-6 h-full rounded-xl border border-cyan-400/30 bg-gradient-to-br from-slate-900/50 via-purple-900/30 to-slate-900/50 backdrop-blur-sm hover:border-cyan-400/60 hover:shadow-xl hover:shadow-cyan-500/25 hover:scale-105 transition-all duration-300">
                {/* Tech corner accents */}
                <div className="absolute top-0 left-0 w-4 h-4 border-t-2 border-l-2 border-cyan-400 rounded-tl-xl" />
                <div className="absolute top-0 right-0 w-4 h-4 border-t-2 border-r-2 border-purple-400 rounded-tr-xl" />
                <div className="absolute bottom-0 left-0 w-4 h-4 border-b-2 border-l-2 border-purple-400 rounded-bl-xl" />
                <div className="absolute bottom-0 right-0 w-4 h-4 border-b-2 border-r-2 border-pink-400 rounded-br-xl" />

                {/* Glow effect */}
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-cyan-500/5 via-purple-500/5 to-pink-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                <div className="relative flex items-center space-x-4 mb-4">
                  <div className="relative">
                    <div className="absolute inset-0 rounded-lg bg-cyan-400/20 blur-sm group-hover:bg-cyan-400/30 transition-all duration-300" />
                    <div className="relative p-3 rounded-lg border border-cyan-400/30 bg-gradient-to-r from-cyan-500/10 via-purple-500/10 to-pink-500/10">
                      <action.icon className="h-6 w-6 text-cyan-300 drop-shadow-lg" />
                    </div>
                  </div>
                  <h3 className="text-lg font-bold bg-gradient-to-r from-cyan-300 via-purple-300 to-pink-300 bg-clip-text text-transparent drop-shadow-lg">
                    {action.title}
                  </h3>
                </div>
                <p className="relative text-slate-300 group-hover:text-white transition-colors duration-300">
                  {action.description}
                </p>
              </div>
            </Link>
          ))}
        </div>

        {/* Activity and Models */}
        <div className="grid gap-6 grid-cols-1 lg:grid-cols-7">
          {/* AI Activity */}
          <div className="lg:col-span-4">
            <div className="glass-card p-6 hover:shadow-xl transition-all duration-300">
              <h3 className="text-xl font-semibold gradient-text mb-4">
                AI Activity
              </h3>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Your recent AI interactions will appear here
                </p>
              </div>
            </div>
          </div>

          {/* Available Models */}
          <div className="lg:col-span-3">
            <div className="glass-card p-6 hover:shadow-xl transition-all duration-300">
              <h3 className="text-xl font-semibold gradient-text mb-4">
                Available Models
              </h3>
              <div className="space-y-4">
                {["DeepSeek", "OpenAI", "Google Gemini", "Ollama"].map(
                  (model) => (
                    <div
                      key={model}
                      className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gradient-to-r hover:from-blue-500/10 hover:via-purple-500/10 hover:to-pink-500/10 transition-all duration-200 group"
                    >
                      <div className="p-1 rounded-md bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-pink-500/20 group-hover:from-blue-500/30 group-hover:via-purple-500/30 group-hover:to-pink-500/30 transition-all duration-200">
                        <Sparkles className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                      </div>
                      <span className="text-sm font-medium">{model}</span>
                    </div>
                  )
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
