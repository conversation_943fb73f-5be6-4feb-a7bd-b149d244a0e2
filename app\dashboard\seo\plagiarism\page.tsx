"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { Loader2 } from "lucide-react";

export default function PlagiarismCheckerPage() {
	const [content, setContent] = useState("");
	const [result, setResult] = useState<{
		similarity: number;
		matches: Array<{ text: string; source: string }>;
	} | null>(null);
	const [loading, setLoading] = useState(false);
	const { toast } = useToast();

	const handleCheck = async () => {
		if (!content.trim()) {
			toast({
				title: "Error",
				description: "Please enter some content to check",
				variant: "destructive",
			});
			return;
		}

		try {
			setLoading(true);
			const response = await fetch("/api/seo/plagiarism", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ content }),
			});

			if (!response.ok) {
				throw new Error("Failed to check plagiarism");
			}

			const data = await response.json();
			setResult(data.result);
		} catch (error) {
            console.error("An error occurred:", error);
			toast({
				title: "Error",
				description: "Failed to check plagiarism. Please try again.",
				variant: "destructive",
			});
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="container mx-auto py-8">
			<h1 className="text-3xl font-bold mb-6">Plagiarism Checker</h1>
			<div className="grid gap-6">
				<div>
					<label className="block mb-2">Content to Check</label>
					<Textarea
						placeholder="Enter your content here to check for plagiarism..."
						value={content}
						onChange={(e) => setContent(e.target.value)}
						rows={8}
					/>
				</div>
				<Button 
					onClick={handleCheck} 
					disabled={loading}
					className="w-full md:w-auto"
				>
					{loading ? (
						<>
							<Loader2 className="mr-2 h-4 w-4 animate-spin" />
							Checking...
						</>
					) : (
						"Check Plagiarism"
					)}
				</Button>
				{result && (
					<div className="space-y-4">
						<div className="p-4 border rounded-lg">
							<h2 className="text-xl font-semibold mb-2">Results</h2>
							<p className="text-lg mb-4">
								Similarity Score: {result.similarity}%
							</p>
							{result.matches.length > 0 ? (
								<div className="space-y-4">
									<h3 className="font-medium">Matched Content:</h3>
									{result.matches.map((match, index) => (
										<div key={index} className="p-3 bg-secondary/20 rounded">
											<p className="mb-2">{match.text}</p>
											<p className="text-sm text-muted-foreground">
												Source: {match.source}
											</p>
										</div>
									))}
								</div>
							) : (
								<p className="text-green-600">No plagiarism detected!</p>
							)}
						</div>
					</div>
				)}
			</div>
		</div>
	);
}