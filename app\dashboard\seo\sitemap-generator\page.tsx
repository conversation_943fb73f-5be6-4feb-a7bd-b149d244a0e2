"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { Loader2 } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";

export default function SitemapGeneratorPage() {
	const [url, setUrl] = useState("");
	const [includeImages, setIncludeImages] = useState(false);
	const [crawlDepth, setCrawlDepth] = useState("3");
	const [loading, setLoading] = useState(false);
	const [result, setResult] = useState("");
	const { toast } = useToast();

	const handleGenerate = async () => {
		if (!url.trim()) {
			toast({
				title: "Error",
				description: "Please enter your website URL",
				variant: "destructive",
			});
			return;
		}

		try {
			setLoading(true);
			const response = await fetch("/api/seo/sitemap-generator", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					url,
					includeImages,
					crawlDepth: parseInt(crawlDepth),
				}),
			});

			if (!response.ok) {
				throw new Error("Failed to generate sitemap");
			}

			const data = await response.json();
			setResult(data.result);
		} catch (error) {
			console.error("An error occurred:", error);
			toast({
				title: "Error",
				description: "Failed to generate sitemap. Please try again.",
				variant: "destructive",
			});
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="container mx-auto py-8">
			<h1 className="text-3xl font-bold mb-6">XML Sitemap Generator</h1>
			<div className="grid gap-6">
				<div>
					<label className="block mb-2">Website URL</label>
					<Input
						placeholder="Enter your website URL"
						value={url}
						onChange={(e) => setUrl(e.target.value)}
					/>
				</div>
				<div className="flex items-center space-x-2">
					<Checkbox
						id="includeImages"
						checked={includeImages}
						onCheckedChange={(checked) => setIncludeImages(checked as boolean)}
					/>
					<label htmlFor="includeImages">Include image sitemaps</label>
				</div>
				<div>
					<label className="block mb-2">Crawl Depth</label>
					<Input
						type="number"
						min="1"
						max="10"
						value={crawlDepth}
						onChange={(e) => setCrawlDepth(e.target.value)}
					/>
				</div>
				<Button 
					onClick={handleGenerate} 
					disabled={loading}
					className="w-full md:w-auto"
				>
					{loading ? (
						<>
							<Loader2 className="mr-2 h-4 w-4 animate-spin" />
							Generating...
						</>
					) : (
						"Generate Sitemap"
					)}
				</Button>
				{result && (
					<div>
						<label className="block mb-2">Generated Sitemap</label>
						<Textarea
							value={result}
							rows={12}
							readOnly
							className="font-mono"
						/>
					</div>
				)}
			</div>
		</div>
	);
}