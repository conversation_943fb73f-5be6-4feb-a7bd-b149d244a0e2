import * as schema from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";

// Create a PostgreSQL connection
const sql = postgres(process.env.DATABASE_URL!);

// Create a Drizzle client with schema and prepare it for queries
export const db = drizzle(sql, { schema });

// Export schema and eq operator for convenience
export { eq, schema };
