"use client";

import * as React from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";

import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { AlertCircle, Bot, Loader2, Send } from "lucide-react";
import { useParams } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { ChatMessage } from "./chat-message";

interface Message {
  id: string;
  role: "user" | "assistant";
  content: string;
  createdAt: Date;
}

interface DeepSeekV3ChatContainerProps {
  id?: string;
}

export function DeepSeekV3ChatContainer({
  id,
}: DeepSeekV3ChatContainerProps): React.ReactElement {
  const params = useParams();
  const chatId = id || (params?.chatId as string);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const {
    data: dbMessages,
    // isLoading: isLoadingMessages,
    error: loadError,
  } = useQuery<Message[], Error>({
    queryKey: ["messages", chatId],
    queryFn: async () => {
      const response = await fetch(`/api/chat/messages?chatId=${chatId}`);
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to load messages");
      }
      const data = await response.json();
      return data.map(
        (msg: {
          id: string;
          role: string;
          content: string;
          createdAt: string;
        }) => ({
          id: msg.id,
          role: msg.role as "user" | "assistant",
          content: msg.content,
          createdAt: new Date(msg.createdAt),
        })
      );
    },
  });

  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isThinking, setIsThinking] = useState(false);

  useEffect(() => {
    if (dbMessages?.length) {
      setMessages(dbMessages);
    }
  }, [dbMessages]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (isLoading || !input.trim()) return;

    setIsLoading(true);
    const userMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content: input,
      createdAt: new Date(),
    };

    try {
      const res = await fetch("/api/chat/deepseekv3/message", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          messages: [...messages, userMessage],
          chatId,
        }),
      });

      if (!res.ok) throw new Error("Failed to send message");
      if (!res.body) throw new Error("No response body");

      // Add user message immediately
      setMessages((prev) => [...prev, userMessage]);
      setInput("");

      const reader = res.body.getReader();
      const decoder = new TextDecoder();
      let currentMessageId = "";
      let currentContent = "";
      const assistantMessageId = Date.now().toString();
      const assistantMessage: Message = {
        id: assistantMessageId,
        role: "assistant",
        content: "",
        createdAt: new Date(),
      };

      setMessages((prev) => [...prev, assistantMessage]);
      setIsThinking(true);

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split("\n");

          for (const line of lines) {
            if (line.startsWith("data: ")) {
              const data = line.slice(5).trim();
              if (data === "[DONE]") continue;

              try {
                const parsed = JSON.parse(data);
                if (parsed.content) {
                  if (currentMessageId !== assistantMessageId) {
                    currentMessageId = assistantMessageId;
                    currentContent = "";
                  }

                  // Skip content between <think> tags
                  if (parsed.content.includes("<think>")) {
                    setIsThinking(true);
                    continue;
                  }
                  if (parsed.content.includes("</think>")) {
                    setIsThinking(false);
                    continue;
                  }

                  // Only add content that's not part of think tags
                  if (
                    !parsed.content.includes("<think>") &&
                    !parsed.content.includes("</think>")
                  ) {
                    currentContent += parsed.content;
                    setMessages((prev) =>
                      prev.map((msg) =>
                        msg.id === assistantMessageId
                          ? { ...msg, content: currentContent }
                          : msg
                      )
                    );
                  }
                }
              } catch (e) {
                console.error("Error parsing chunk:", e);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
        setIsThinking(false);
      }

      setIsLoading(false);
      await queryClient.invalidateQueries({ queryKey: ["messages", chatId] });
    } catch (error) {
      console.error("Chat error:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to send message",
      });
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value);
  };

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const handleKeyDown = async (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      if (isLoading || !input.trim()) return;

      // Create a synthetic form event and call handleSubmit directly
      const syntheticEvent = {
        preventDefault: () => {},
        currentTarget: e.currentTarget.form,
      } as React.FormEvent<HTMLFormElement>;
      await handleSubmit(syntheticEvent);
    }
  };

  return (
    <div className="flex h-full flex-col">
      <Card className="rounded-none border-x-0 border-t-0 bg-background/95 dark:bg-background/95">
        <div className="flex items-center justify-between border-b px-4 py-3 dark:border-border">
          <div className="flex items-center gap-2">
            <Bot className="h-5 w-5 text-primary dark:text-primary" />
            <h2 className="text-lg font-semibold tracking-tight text-foreground dark:text-foreground">
              Chat with DeepSeek V3
            </h2>
          </div>
        </div>
      </Card>

      {loadError && (
        <Alert variant="destructive" className="m-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load messages: {loadError.message}
          </AlertDescription>
        </Alert>
      )}

      <ScrollArea className="flex-1 px-4 py-4">
        {messages.map((message) => (
          <ChatMessage key={message.id} message={message} />
        ))}
        {isThinking && (
          <div className="flex items-center gap-2 px-4 py-2 rounded bg-muted/50 dark:bg-muted">
            <Loader2 className="h-4 w-4 animate-spin" />
            <p className="text-sm text-muted-foreground dark:text-muted-foreground">
              DeepSeek is thinking...
            </p>
          </div>
        )}
        <div ref={messagesEndRef} />
      </ScrollArea>

      <Card className="rounded-none border-x-0 border-b-0">
        <form onSubmit={handleSubmit} className="flex gap-3 p-4" noValidate>
          <Textarea
            placeholder="Send a message..."
            value={input}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            className="min-h-[60px] w-full resize-none"
            disabled={isLoading}
          />
          <Button type="submit" disabled={isLoading || !input.trim()}>
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </form>
      </Card>
    </div>
  );
}
