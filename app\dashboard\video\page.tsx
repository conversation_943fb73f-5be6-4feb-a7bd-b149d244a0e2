"use client";

import { Wand2, <PERSON><PERSON><PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import Link from "next/link";

const videoTools = [
  {
    title: "AI Video Generation",
    description: "Generate custom videos using AI with text prompts.",
    icon: Sparkles,
    href: "/dashboard/video/generate",
    badge: "New",
    features: [
      "Text-to-Video",
      "Custom Prompts",
      "HD Quality",
      "Fast Generation",
    ],
  },
];

export default function VideoPage() {
  return (
    <div className="container mx-auto py-8">
      <div className="flex flex-col gap-6">
        <div>
          <h1 className="text-4xl font-bold gradient-text mb-2">Video Tools</h1>
          <p className="text-muted-foreground">
            Create and find videos using our AI-powered tools
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {videoTools.map((tool, index) => (
            <Card key={index} className="p-6 hover:shadow-lg transition-shadow">
              <div className="flex flex-col gap-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-primary/10">
                      <tool.icon className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold">{tool.title}</h3>
                      {tool.badge && (
                        <span
                          className={`inline-block px-2 py-1 text-xs rounded-full ${
                            tool.badge === "New"
                              ? "bg-green-100 text-green-800"
                              : "bg-gray-100 text-gray-800"
                          }`}
                        >
                          {tool.badge}
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                <p className="text-muted-foreground">{tool.description}</p>

                <div className="flex flex-wrap gap-2">
                  {tool.features.map((feature, featureIndex) => (
                    <span
                      key={featureIndex}
                      className="px-2 py-1 text-xs bg-secondary rounded-md"
                    >
                      {feature}
                    </span>
                  ))}
                </div>

                <div className="mt-auto">
                  {tool.href === "#" ? (
                    <Button variant="outline" disabled className="w-full">
                      <Wand2 className="mr-2 h-4 w-4" />
                      Coming Soon
                    </Button>
                  ) : (
                    <Link href={tool.href}>
                      <Button className="w-full">
                        <Wand2 className="mr-2 h-4 w-4" />
                        Try {tool.title}
                      </Button>
                    </Link>
                  )}
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
