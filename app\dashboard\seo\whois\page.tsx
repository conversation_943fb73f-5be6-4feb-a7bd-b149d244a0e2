"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Globe, Calendar, Building2, Mail } from "lucide-react";
import { Card } from "@/components/ui/card";

interface WhoisResult {
  domain: string;
  registrar: {
    name: string;
    url: string;
    whoisServer: string;
  };
  registrant: {
    organization: string;
    country: string;
    email?: string;
  };
  dates: {
    creationDate: string;
    expiryDate: string;
    updatedDate: string;
  };
  nameservers: string[];
  status: string[];
}

export default function WhoisCheckerPage() {
  const [domain, setDomain] = useState("");
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<WhoisResult | null>(null);
  const { toast } = useToast();

  const handleCheck = async () => {
    if (!domain.trim()) {
      toast({
        title: "Error",
        description: "Please enter a domain name",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);
      const response = await fetch("/api/seo/whois", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ domain }),
      });

      if (!response.ok) {
        throw new Error("Failed to fetch WHOIS information");
      }

      const data = await response.json();
      setResult(data.result);
    } catch (err) {
      console.error("An error occurred:", err);
      toast({
        title: "Error",
        description: "Failed to fetch WHOIS information. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">WHOIS Checker</h1>
      <div className="grid gap-6">
        <div>
          <label className="block mb-2">Domain Name</label>
          <Input
            type="text"
            placeholder="Enter domain name (e.g., example.com)"
            value={domain}
            onChange={(e) => setDomain(e.target.value)}
          />
        </div>
        <Button
          onClick={handleCheck}
          disabled={loading}
          className="w-full md:w-auto"
        >
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Checking...
            </>
          ) : (
            "Check WHOIS"
          )}
        </Button>

        {result && (
          <div className="space-y-6">
            <Card className="p-6">
              <div className="flex items-center gap-3 mb-6">
                <Globe className="h-6 w-6 text-primary" />
                <h2 className="text-2xl font-semibold">{result.domain}</h2>
              </div>

              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  {/* Registrar Information */}
                  <div className="p-4 bg-secondary/20 rounded-lg">
                    <h3 className="font-medium mb-3">Registrar Information</h3>
                    <div className="space-y-2">
                      <p className="text-sm">
                        <span className="font-medium">Name:</span>{" "}
                        {result.registrar.name}
                      </p>
                      {result.registrar.url && (
                        <p className="text-sm">
                          <span className="font-medium">Website:</span>{" "}
                          <a
                            href={result.registrar.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-primary hover:underline"
                          >
                            {result.registrar.url}
                          </a>
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Important Dates */}
                  <div className="p-4 bg-secondary/20 rounded-lg">
                    <h3 className="font-medium mb-3">Important Dates</h3>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <p className="text-sm">
                          <span className="font-medium">Created:</span>{" "}
                          {new Date(
                            result.dates.creationDate
                          ).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <p className="text-sm">
                          <span className="font-medium">Expires:</span>{" "}
                          {new Date(
                            result.dates.expiryDate
                          ).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <p className="text-sm">
                          <span className="font-medium">Last Updated:</span>{" "}
                          {new Date(
                            result.dates.updatedDate
                          ).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  {/* Registrant Information */}
                  <div className="p-4 bg-secondary/20 rounded-lg">
                    <h3 className="font-medium mb-3">Registrant Information</h3>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Building2 className="h-4 w-4" />
                        <p className="text-sm">
                          <span className="font-medium">Organization:</span>{" "}
                          {result.registrant.organization || "Private"}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Globe className="h-4 w-4" />
                        <p className="text-sm">
                          <span className="font-medium">Country:</span>{" "}
                          {result.registrant.country}
                        </p>
                      </div>
                      {result.registrant.email && (
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4" />
                          <p className="text-sm">
                            <span className="font-medium">Email:</span>{" "}
                            {result.registrant.email}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Nameservers */}
                  <div className="p-4 bg-secondary/20 rounded-lg">
                    <h3 className="font-medium mb-3">Nameservers</h3>
                    <div className="space-y-1">
                      {result.nameservers.map((ns, index) => (
                        <p key={index} className="text-sm">
                          {ns}
                        </p>
                      ))}
                    </div>
                  </div>

                  {/* Domain Status */}
                  <div className="p-4 bg-secondary/20 rounded-lg">
                    <h3 className="font-medium mb-3">Domain Status</h3>
                    <div className="flex flex-wrap gap-2">
                      {result.status.map((status, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-primary/10 rounded-full text-sm"
                        >
                          {status}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}
