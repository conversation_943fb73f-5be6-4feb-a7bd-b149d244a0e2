import { create } from 'zustand';
import { CreateUserInput, User } from '@/types/index';
import { signUp } from '@/lib/actions/auth';
import { deleteAgencyUser } from '@/lib/actions/user';

interface UserStore {
	users: User[];
	userCount: number;
	isLoading: boolean;
	addUser: (user: CreateUserInput) => Promise<void>;
	removeUser: (id: string) => Promise<void>;
	setUsers: (users: User[]) => void;
	setLoading: (loading: boolean) => void;
}

export const useUserStore = create<UserStore>((set) => ({
	users: [],
	userCount: 0,
	isLoading: false,
	setLoading: (loading: boolean) => set({ isLoading: loading }),

	addUser: async (userData) => {
		try {
			set({ isLoading: true });
			const formData = new FormData();
			formData.append('name', userData.name);
			formData.append('email', userData.email);
			formData.append('password', userData.password);
			formData.append('userType', userData.userType);
			formData.append('isAgencyUser', 'true'); // Mark this as agency user creation

			const result = await signUp(formData);
			
			if (result.error) {
				throw new Error(result.error);
			}

			if (result.success && result.data) {
				const newUser: User = {
					...result.data,
					createdAt: new Date()
				};
				set((state) => ({
					users: [...state.users, newUser],
					userCount: state.users.length + 1,
				}));
			}
		} catch (error) {
			throw error;
		} finally {
			set({ isLoading: false });
		}
	},
	
	removeUser: async (id) => {
		try {
			set({ isLoading: true });
			const result = await deleteAgencyUser(id);
			
			if (result.error) {
				throw new Error(result.error);
			}

			set((state) => ({
				users: state.users.filter((user) => user.id !== id),
				userCount: state.users.length - 1,
			}));
		} catch (error) {
			throw error;
		} finally {
			set({ isLoading: false });
		}
	},

	setUsers: (users) => set({
		users,
		userCount: users.length,
	}),
}));


