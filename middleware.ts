import { withAuth } from "next-auth/middleware";
import { NextResponse } from "next/server";
import type { NextRequest } from 'next/server';

function corsMiddleware(request: NextRequest) {
  // Only apply to API routes
  if (request.nextUrl.pathname.startsWith('/api/')) {
    // Handle preflight requests
    if (request.method === 'OPTIONS') {
      return new NextResponse(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Chat-Id',
          'Access-Control-Max-Age': '86400',
        },
      });
    }

    // Handle actual requests
    const response = NextResponse.next();
    response.headers.set('Access-Control-Allow-Origin', '*');
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Chat-Id');

    return response;
  }

  return NextResponse.next();
}

export default withAuth(
  function middleware(req) {
    // Handle CORS first
    if (req.nextUrl.pathname.startsWith('/api/')) {
      return corsMiddleware(req);
    }

    const { pathname } = req.nextUrl;
    const isAuth = !!req.nextauth.token;

    // Public paths that should redirect to dashboard if authenticated
    const publicPaths = [
      "/login", 
      "/register", 
      "/", 
      "/basic-signup",
      "/unlimited-lite-signup",
      "/unlimited-premium-signup",
      "/agency-basic-signup",
      "/agency-deluxe-signup"
    ];
    const isPublicPath = publicPaths.some((path) => pathname === path);

    if (isAuth && isPublicPath) {
      return NextResponse.redirect(new URL("/dashboard", req.url));
    }

    return NextResponse.next();
    },
    {
    callbacks: {
      authorized: ({ req, token }) => {
      const { pathname } = req.nextUrl;
      
      // Always allow OPTIONS requests for CORS
      if (req.method === 'OPTIONS') {
        return true;
      }

        const publicPaths = [
        "/login", 
        "/register", 
        "/",
        "/basic-signup",
        "/unlimited-lite-signup",
        "/unlimited-premium-signup",
        "/agency-basic-signup",
        "/agency-deluxe-signup"
        ];
      
      // Allow public paths without authentication
      if (publicPaths.includes(pathname)) {
        return true;
      }

      // Require authentication for all other paths
      return !!token;
      },
    },
    }
  );

  export const config = {
    matcher: [
    // Protected routes that need authentication
    "/dashboard/:path*",
    "/api/:path*",
    
    // Public routes that should redirect to dashboard if authenticated
    "/login",
    "/register",
    "/",
    "/basic-signup",
    "/unlimited-lite-signup",
    "/unlimited-premium-signup",
    "/agency-basic-signup",
    "/agency-deluxe-signup"
  ],
};