"use client";

import { LogoutButton } from "@/components/auth/logout-button";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { CreditsDisplay } from "@/components/ui/credits-display";
import { cn } from "@/lib/utils";
import { useUsersStore } from "@/store/use-users-store";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  Gift,
  GraduationCap,
  HeadphonesIcon,
  ImageIcon,
  LayoutDashboard,
  MessageSquare,
  Plus,
  Search,
  Settings,
  Trash2,
  Users,
  Video,
} from "lucide-react";
import { nanoid } from "nanoid";
import { useSession } from "next-auth/react";
import NextImage from "next/image";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useRef, useEffect, useState } from "react";

interface Chat {
  id: string;
  title: string;
  createdAt: Date;
}

async function fetchChats(): Promise<Chat[]> {
  const response = await fetch("/api/chat/list");
  if (!response.ok) throw new Error("Failed to load chats");
  const data = await response.json();
  return data.map((chat: Chat) => ({
    ...chat,
    createdAt: new Date(chat.createdAt),
  }));
}

async function deleteChat(chatId: string) {
  const response = await fetch("/api/chat/delete", {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ chatId }),
  });
  if (!response.ok) throw new Error("Failed to delete chat");
  return response.json();
}

async function createChat() {
  const response = await fetch("/api/chat/gemini/create", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      id: nanoid(),
      title: "New Chat",
    }),
  });
  if (!response.ok) throw new Error("Failed to create chat");
  return response.json();
}

const mainFeatures = [
  {
    title: "Dashboard",
    icon: LayoutDashboard,
    href: "/dashboard",
    description: "Overview and quick access",
  },
  {
    title: "AI Chat Hub",
    icon: MessageSquare,
    href: "/dashboard/chat",
    description: "GPT-5, Grok, DeepSeek, GPT-4, Gemini, Ollama",
  },
  {
    title: "AI Image Generation",
    icon: ImageIcon,
    href: "/dashboard/image",
    description: "Grok, Midjourney, DALL-E, DeepSeek, Gemini",
  },
  {
    title: "AI Video Generation",
    icon: Video,
    href: "/dashboard/video/generate",
    description: "Create stunning AI-powered videos",
  },
];

const additionalTools = [
  {
    title: "250+ AI Tools",
    icon: Search,
    href: "/dashboard/seo",
    description: "Complete AI toolkit",
    badge: "Pro",
  },
];
const supportFeatures = [
  {
    title: "Training Hub",
    icon: GraduationCap,
    href: "/dashboard/training",
    description: "Tutorials and learning resources",
  },
  {
    title: "Bonus Perks",
    icon: Gift,
    href: "/dashboard/bonus",
    description: "Exclusive rewards and benefits",
    badge: "Pro",
  },
  {
    title: "Support Talk",
    icon: HeadphonesIcon,
    href: "/dashboard/support",
    description: "Chat with our admin team",
  },
];

type SidebarProps = React.HTMLAttributes<HTMLDivElement>;

export function Sidebar({ className, ...props }: SidebarProps) {
  const pathname = usePathname();
  const router = useRouter();
  const queryClient = useQueryClient();
  const { data: session } = useSession();

  const { userCount } = useUsersStore();

  // Scroll detection for sidebar
  const scrollRef = useRef<HTMLDivElement>(null);
  const [canScroll, setCanScroll] = useState(false);
  const [isScrolledToBottom, setIsScrolledToBottom] = useState(false);

  useEffect(() => {
    const checkScrollable = () => {
      if (scrollRef.current) {
        const { scrollHeight, clientHeight, scrollTop } = scrollRef.current;
        const scrollable = scrollHeight > clientHeight;
        const atBottom = scrollTop + clientHeight >= scrollHeight - 5; // 5px threshold

        setCanScroll(scrollable);
        setIsScrolledToBottom(atBottom);
      }
    };

    const handleScroll = () => {
      checkScrollable();
    };

    const scrollElement = scrollRef.current;
    if (scrollElement) {
      scrollElement.addEventListener("scroll", handleScroll);
      // Check initially
      checkScrollable();

      // Check on resize
      const resizeObserver = new ResizeObserver(checkScrollable);
      resizeObserver.observe(scrollElement);

      return () => {
        scrollElement.removeEventListener("scroll", handleScroll);
        resizeObserver.disconnect();
      };
    }
  }, []);

  const isAdmin =
    (session?.user as { userType?: string })?.userType === "admin";

  const isAgencyUser =
    (session?.user as { userType?: string })?.userType === "agency-basic" ||
    (session?.user as { userType?: string })?.userType === "agency-deluxe";

  const adminFeatures = isAdmin
    ? [
        {
          title: "User Management",
          icon: Users,
          href: "/dashboard/users",
          description: "Manage user accounts",
          badge: userCount > 0 ? userCount.toString() : undefined,
        },
        {
          title: "Share with Users",
          icon: Users,
          href: "/dashboard/share",
          description: "Share with users",
        },
      ]
    : [];

  const agencyFeatures = isAgencyUser
    ? [
        {
          title: "User Management",
          icon: Users,
          href: "/dashboard/users-management",
          description: "Manage your organization users",
          badge: "Agency",
        },
      ]
    : [];

  const { data: chats = [], isLoading } = useQuery({
    queryKey: ["chats"],
    queryFn: fetchChats,
  });

  const deleteChatMutation = useMutation({
    mutationFn: deleteChat,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["chats"] });
    },
  });

  const createChatMutation = useMutation({
    mutationFn: createChat,
    onSuccess: (newChat) => {
      queryClient.invalidateQueries({ queryKey: ["chats"] });
      router.push(`/dashboard/chat/gemini/${newChat.id}`);
    },
  });

  return (
    <div className={cn("relative h-full flex flex-col", className)} {...props}>
      {/* Unique Neon-Tech Background */}
      <div className="absolute inset-0 bg-gradient-to-b from-slate-900 via-purple-900 to-slate-900 dark:from-black dark:via-purple-950 dark:to-black" />
      <div className="absolute inset-0 bg-gradient-to-r from-cyan-500/10 via-purple-500/20 to-pink-500/10" />

      {/* Geometric Pattern Overlay */}
      <div
        className="absolute inset-0 opacity-20"
        style={{
          backgroundImage: `
          radial-gradient(circle at 25% 25%, #06b6d4 0%, transparent 50%),
          radial-gradient(circle at 75% 75%, #8b5cf6 0%, transparent 50%),
          linear-gradient(45deg, transparent 40%, rgba(236, 72, 153, 0.1) 50%, transparent 60%)
        `,
        }}
      />

      {/* Animated Tech Lines */}
      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-cyan-400 to-transparent animate-pulse" />
      <div className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-pink-400 to-transparent animate-pulse delay-1000" />
      <div className="absolute top-0 right-0 w-px h-full bg-gradient-to-b from-transparent via-purple-400 to-transparent animate-pulse delay-500" />

      {/* Floating Tech Elements */}
      <div className="absolute top-10 right-4 w-2 h-2 bg-cyan-400 rounded-full animate-ping" />
      <div className="absolute top-32 left-4 w-1 h-1 bg-pink-400 rounded-full animate-ping delay-1000" />
      <div className="absolute bottom-20 right-6 w-1.5 h-1.5 bg-purple-400 rounded-full animate-ping delay-2000" />

      {/* Hexagonal Pattern */}
      <div
        className="absolute inset-0 opacity-5"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }}
      />

      {/* Content with relative positioning */}
      <div className="relative z-10 h-full flex flex-col">
        {/* Main content area with scroll */}
        <div
          ref={scrollRef}
          className={cn(
            "flex-1 sidebar-scroll",
            canScroll && !isScrolledToBottom && "scroll-fade-bottom"
          )}
        >
          <div className="space-y-4 py-4">
            <div className="px-3 py-2">
              <div className="mb-6 px-4 pt-10 md:pt-4">
                <div className="relative group">
                  {/* Tech border frame */}
                  <div className="absolute inset-0 rounded-xl border border-cyan-400/30 bg-gradient-to-r from-cyan-500/10 via-purple-500/10 to-pink-500/10 backdrop-blur-sm" />
                  <div className="absolute top-0 left-0 w-3 h-3 border-t-2 border-l-2 border-cyan-400 rounded-tl-xl" />
                  <div className="absolute top-0 right-0 w-3 h-3 border-t-2 border-r-2 border-purple-400 rounded-tr-xl" />
                  <div className="absolute bottom-0 left-0 w-3 h-3 border-b-2 border-l-2 border-purple-400 rounded-bl-xl" />
                  <div className="absolute bottom-0 right-0 w-3 h-3 border-b-2 border-r-2 border-pink-400 rounded-br-xl" />

                  <div className="relative flex items-center gap-3 p-4">
                    <div className="relative">
                      <div className="absolute inset-0 rounded-lg bg-cyan-400/20 blur-sm" />
                      <NextImage
                        src="/icon.png"
                        alt="AI Vora Logo"
                        width={28}
                        height={28}
                        className="relative h-7 w-7 drop-shadow-lg"
                      />
                    </div>
                    <h2 className="text-xl font-bold bg-gradient-to-r from-cyan-300 via-purple-300 to-pink-300 bg-clip-text text-transparent drop-shadow-lg">
                      AI Vora
                    </h2>
                  </div>
                </div>
              </div>
              <div className="space-y-1">
                {mainFeatures.map((item) => (
                  <Link key={item.href} href={item.href}>
                    <div
                      className={cn(
                        "group relative flex items-center px-4 py-3 mx-2 rounded-xl transition-all duration-300 border border-transparent",
                        "hover:border-cyan-400/50 hover:bg-gradient-to-r hover:from-cyan-500/10 hover:via-purple-500/10 hover:to-pink-500/10",
                        "hover:shadow-lg hover:shadow-cyan-500/25 hover:scale-105",
                        pathname === item.href &&
                          "border-cyan-400/70 bg-gradient-to-r from-cyan-500/20 via-purple-500/20 to-pink-500/20 shadow-lg shadow-cyan-500/30 scale-105"
                      )}
                    >
                      {/* Neon glow effect for active item */}
                      {pathname === item.href && (
                        <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-cyan-400/20 via-purple-400/20 to-pink-400/20 blur-sm" />
                      )}

                      {/* Tech corner accents */}
                      <div className="absolute top-0 left-0 w-2 h-2 border-t-2 border-l-2 border-cyan-400/50 rounded-tl-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      <div className="absolute bottom-0 right-0 w-2 h-2 border-b-2 border-r-2 border-pink-400/50 rounded-br-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      <div className="relative mr-3">
                        {/* Icon background glow */}
                        <div
                          className={cn(
                            "absolute inset-0 rounded-lg transition-all duration-300",
                            pathname === item.href
                              ? "bg-cyan-400/20 shadow-lg shadow-cyan-400/50"
                              : "group-hover:bg-cyan-400/10"
                          )}
                        />
                        <item.icon
                          className={cn(
                            "relative h-5 w-5 transition-all duration-300",
                            pathname === item.href
                              ? "text-cyan-300 drop-shadow-lg"
                              : "text-slate-300 group-hover:text-cyan-300 group-hover:drop-shadow-lg"
                          )}
                        />
                      </div>
                      <div className="relative">
                        <div
                          className={cn(
                            "font-semibold transition-all duration-300",
                            pathname === item.href
                              ? "text-white drop-shadow-lg"
                              : "text-slate-200 group-hover:text-white group-hover:drop-shadow-lg"
                          )}
                        >
                          {item.title}
                        </div>
                        <p
                          className={cn(
                            "text-xs transition-all duration-300",
                            pathname === item.href
                              ? "text-cyan-200"
                              : "text-slate-400 group-hover:text-cyan-200"
                          )}
                        >
                          {item.description}
                        </p>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>

            {agencyFeatures.length > 0 && (
              <div className="px-3 py-2">
                <h2 className="mb-4 px-4 text-xl font-semibold gradient-text">
                  Agency Features
                </h2>
                <div className="space-y-1">
                  {agencyFeatures.map((item) => (
                    <Link key={item.href} href={item.href}>
                      <div
                        className={cn(
                          "flex items-center px-4 py-2 rounded-lg transition-colors",
                          "hover:bg-white/5 dark:hover:bg-white/5",
                          pathname === item.href && "bg-primary/10 text-primary"
                        )}
                      >
                        <item.icon
                          className={cn(
                            "mr-3 h-5 w-5",
                            pathname === item.href
                              ? "text-primary"
                              : "text-muted-foreground"
                          )}
                        />
                        <div className="flex items-center justify-between flex-1">
                          <div>
                            <div className="font-medium">{item.title}</div>
                            <p className="text-xs text-muted-foreground">
                              {item.description}
                            </p>
                          </div>
                          {item.badge && (
                            <span className="text-xs font-medium bg-primary/20 text-primary px-2 py-0.5 rounded">
                              {item.badge}
                            </span>
                          )}
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            )}

            {adminFeatures.length > 0 && (
              <div className="px-3 py-2">
                <h2 className="mb-4 px-4 text-xl font-semibold gradient-text">
                  Admin Features
                </h2>
                <div className="space-y-1">
                  {adminFeatures.map((item) => (
                    <Link key={item.href} href={item.href}>
                      <div
                        className={cn(
                          "flex items-center px-4 py-2 rounded-lg transition-colors",
                          "hover:bg-white/5 dark:hover:bg-white/5",
                          pathname === item.href && "bg-primary/10 text-primary"
                        )}
                      >
                        <item.icon
                          className={cn(
                            "mr-3 h-5 w-5",
                            pathname === item.href
                              ? "text-primary"
                              : "text-muted-foreground"
                          )}
                        />
                        <div className="flex items-center justify-between flex-1">
                          <div>
                            <div className="font-medium">{item.title}</div>
                            <p className="text-xs text-muted-foreground">
                              {item.description}
                            </p>
                          </div>
                          {item.badge && (
                            <span className="text-xs font-medium bg-primary/20 text-primary px-2 py-0.5 rounded">
                              {item.badge}
                            </span>
                          )}
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            )}

            <div className="px-3 py-2">
              <h2 className="mb-4 px-4 text-xl font-semibold gradient-text">
                Pro Tools
              </h2>
              <div className="space-y-1">
                {additionalTools.map((item) => (
                  <Link key={item.href} href={`${item.href}`}>
                    <div
                      className={cn(
                        "flex items-center px-4 py-2 rounded-lg transition-colors",
                        "hover:bg-white/5 dark:hover:bg-white/5",
                        pathname === item.href && "bg-primary/10 text-primary"
                      )}
                    >
                      <item.icon
                        className={cn(
                          "mr-3 h-5 w-5",
                          pathname === item.href
                            ? "text-primary"
                            : "text-muted-foreground"
                        )}
                      />
                      <div className="flex items-center justify-between flex-1">
                        <div>
                          <div className="font-medium">{item.title}</div>
                          <p className="text-xs text-muted-foreground">
                            {item.description}
                          </p>
                        </div>
                        {item.badge && (
                          <span className="text-xs font-medium bg-primary/20 text-primary px-2 py-0.5 rounded">
                            {item.badge}
                          </span>
                        )}
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>

            <div className="px-3 py-2">
              <h2 className="mb-4 px-4 text-xl font-semibold gradient-text">
                Support & Resources
              </h2>
              <div className="space-y-1">
                {supportFeatures.map((item) => (
                  <Link key={item.href} href={item.href}>
                    <div
                      className={cn(
                        "flex items-center px-4 py-2 rounded-lg transition-colors",
                        "hover:bg-white/5 dark:hover:bg-white/5",
                        pathname === item.href && "bg-primary/10 text-primary"
                      )}
                    >
                      <item.icon
                        className={cn(
                          "mr-3 h-5 w-5",
                          pathname === item.href
                            ? "text-primary"
                            : "text-muted-foreground"
                        )}
                      />
                      <div className="flex items-center justify-between flex-1">
                        <div>
                          <div className="font-medium">{item.title}</div>
                          <p className="text-xs text-muted-foreground">
                            {item.description}
                          </p>
                        </div>
                        {item.badge && (
                          <span className="text-xs font-medium bg-primary/20 text-primary px-2 py-0.5 rounded">
                            {item.badge}
                          </span>
                        )}
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>

            <div className="px-3 py-2">
              <h2 className="mb-4 px-4 text-xl font-semibold gradient-text">
                Chats
              </h2>
              <ScrollArea className="flex-1">
                <div className="space-y-2 p-2">
                  <Button
                    onClick={() => createChatMutation.mutate()}
                    className="w-full justify-start"
                    variant="outline"
                    disabled={createChatMutation.isPending}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    {createChatMutation.isPending ? "Creating..." : "New Chat"}
                  </Button>
                  {isLoading ? (
                    <div className="flex items-center justify-center py-4">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                    </div>
                  ) : chats.length === 0 ? (
                    <div className="text-center text-sm text-muted-foreground py-4">
                      No chats yet
                    </div>
                  ) : (
                    [...chats]
                      .sort(
                        (a, b) =>
                          new Date(b.createdAt).getTime() -
                          new Date(a.createdAt).getTime()
                      )
                      .slice(0, 5)
                      .map((chat) => (
                        <div
                          key={chat.id}
                          className={cn(
                            "group flex w-full items-center justify-between rounded-lg px-2 py-2 hover:bg-accent/50",
                            pathname.includes(chat.id) && "bg-accent"
                          )}
                        >
                          <Link
                            href={`/dashboard/chat/gemini/${chat.id}`}
                            className="flex-1 truncate"
                          >
                            <span className="line-clamp-1 text-sm">
                              {chat.title || "New Chat"}
                            </span>
                          </Link>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity"
                            onClick={(e) => {
                              e.preventDefault();
                              deleteChatMutation.mutate(chat.id);
                            }}
                          >
                            <Trash2 className="h-4 w-4 text-muted-foreground hover:text-destructive" />
                          </Button>
                        </div>
                      ))
                  )}
                </div>
              </ScrollArea>
            </div>
          </div>
        </div>

        {/* Credits Display */}
        <div className="mt-auto">
          <CreditsDisplay />
        </div>

        {/* Scroll indicator - only show if content is scrollable and not at bottom */}
        {canScroll && !isScrolledToBottom && (
          <div className="flex items-center justify-center py-2 animate-in fade-in-50 duration-500">
            <div className="flex items-center space-x-2 px-3 py-1 rounded-full bg-gradient-to-r from-cyan-500/10 via-purple-500/10 to-pink-500/10 border border-cyan-400/20">
              <div className="flex space-x-1">
                <div className="w-1 h-1 bg-cyan-400/60 rounded-full animate-pulse" />
                <div className="w-1 h-1 bg-purple-400/60 rounded-full animate-pulse delay-75" />
                <div className="w-1 h-1 bg-pink-400/60 rounded-full animate-pulse delay-150" />
              </div>
              <div className="text-xs text-cyan-400/80 font-medium">
                Scroll for more
              </div>
            </div>
          </div>
        )}

        {/* Footer section - always at bottom */}
        <div className="border-t border-white/20 dark:border-white/10">
          <div className="p-4 space-y-3">
            <Link href="/dashboard/settings">
              <div
                className={cn(
                  "flex items-center px-4 py-2 rounded-lg transition-colors glass-card",
                  "hover:bg-gradient-to-r hover:from-blue-500/10 hover:via-purple-500/10 hover:to-pink-500/10",
                  pathname === "/dashboard/settings" &&
                    "bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-pink-500/20 text-blue-600 dark:text-blue-400"
                )}
              >
                <Settings
                  className={cn(
                    "mr-3 h-5 w-5",
                    pathname === "/dashboard/settings"
                      ? "text-blue-600 dark:text-blue-400"
                      : "text-muted-foreground"
                  )}
                />
                <span className="font-medium">Settings</span>
              </div>
            </Link>
            <LogoutButton />
          </div>
        </div>
      </div>
    </div>
  );
}
