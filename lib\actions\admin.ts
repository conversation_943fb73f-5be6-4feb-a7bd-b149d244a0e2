"use server";

import { users } from "@/lib/db/schema";
import { db } from "@/lib/db";
import { hash } from "bcryptjs";
import { eq, ne, or } from "drizzle-orm";
import { getServerSession } from "next-auth";
import { z } from "zod";
import { authOptions } from "../auth/auth.config";
import { sendEmail } from "../email";

interface CustomUser {
  id: string;
  email: string;
  name?: string | null;
  userType:
    | "admin"
    | "basic"
    | "unlimited-lite"
    | "unlimited-premium"
    | "agency-basic"
    | "agency-deluxe";
}

const passwordSchema = z.object({
  password: z.string().min(6, "Password must be at least 6 characters"),
});

export async function getUsers() {
  try {
    const session = await getServerSession(authOptions);
    const user = session?.user as CustomUser | undefined;

    if (!user || user.userType !== "admin") {
      return {
        success: false,
        error: "You must be an administrator to perform this action",
      };
    }

    const allUsers = await db.query.users.findMany({
      where: ne(users.userType, "admin"),
    });

    return {
      success: true,
      data: allUsers,
      message: `Successfully retrieved ${allUsers.length} users`,
    };
  } catch (error) {
    console.error("Error fetching users:", error);
    return {
      success: false,
      error: "An error occurred while fetching users. Please try again later.",
    };
  }
}

type ShareContent = {
  title: string;
  description: string;
  link?: string;
  type: "demo" | "issue" | "announcement";
  targetEmails: string[];
};

export async function shareWithUsers(content: ShareContent) {
  try {
    // Validate session
    const session = await getServerSession(authOptions);
    const user = session?.user as CustomUser | undefined;

    if (!user || user.userType !== "admin") {
      return {
        success: false,
        error: "You must be an administrator to share content",
      };
    }

    // Validate content
    if (!content.title || !content.description) {
      return {
        success: false,
        error: "Title and description are required",
      };
    }

    // Get users based on targeting
    let targetUsers;
    if (content.targetEmails.length > 0) {
      // Validate email format
      const invalidEmails = content.targetEmails.filter(
        (email) => !email.includes("@")
      );
      if (invalidEmails.length > 0) {
        return {
          success: false,
          error: `Invalid email format for: ${invalidEmails.join(", ")}`,
        };
      }

      targetUsers = await db.query.users.findMany({
        where: or(
          ...content.targetEmails.map((email) => eq(users.email, email))
        ),
      });

      // Check if all target emails were found
      const foundEmails = targetUsers.map((u) => u.email);
      const notFoundEmails = content.targetEmails.filter(
        (email) => !foundEmails.includes(email)
      );

      if (notFoundEmails.length > 0) {
        return {
          success: false,
          error: `Some users were not found: ${notFoundEmails.join(", ")}`,
        };
      }
    } else {
      targetUsers = await db.query.users.findMany({
        where: ne(users.userType, "admin"),
      });

      if (targetUsers.length === 0) {
        return {
          success: false,
          error: "No users found to share content with",
        };
      }
    }

    const emailTemplate = `
			<!DOCTYPE html>
			<html>
				<head>
					<style>
						body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
						.container { max-width: 600px; margin: 0 auto; padding: 20px; }
						.header { background-color: #f8f9fa; padding: 20px; text-align: center; }
						.content { padding: 20px; }
					</style>
				</head>
				<body>
					<div class="container">
						<div class="header">
							<h1>${content.type.charAt(0).toUpperCase() + content.type.slice(1)}: ${
      content.title
    }</h1>
						</div>
						<div class="content">
							<p>${content.description}</p>
							${
                content.link
                  ? `<p>Link: <a href="${content.link}">${content.link}</a></p>`
                  : ""
              }
						</div>
					</div>
				</body>
			</html>
		`;

    // Send emails to targeted users
    const emailResults = await Promise.allSettled(
      targetUsers.map((user) =>
        sendEmail({
          to: user.email,
          subject: `${
            content.type.charAt(0).toUpperCase() + content.type.slice(1)
          }: ${content.title}`,
          html: emailTemplate,
        })
      )
    );

    // Check for failed emails
    const failedEmails = emailResults
      .map((result, index) =>
        result.status === "rejected" ? targetUsers[index].email : null
      )
      .filter((email): email is string => email !== null);

    if (failedEmails.length > 0) {
      return {
        success: false,
        error: `Failed to send emails to: ${failedEmails.join(", ")}`,
        partialSuccess: true,
        successCount: targetUsers.length - failedEmails.length,
      };
    }

    return {
      success: true,
      message: `Content successfully shared with ${targetUsers.length} users`,
    };
  } catch (error) {
    console.error("Error sharing content:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to share content with users",
    };
  }
}

export async function resetUserPassword(userId: string, newPassword: string) {
  try {
    const session = await getServerSession(authOptions);
    const user = session?.user as CustomUser | undefined;

    if (!user || user.userType !== "admin") {
      return {
        success: false,
        error: "You must be an administrator to reset passwords",
      };
    }

    // Validate password with real-time feedback
    const validationResult = passwordSchema.safeParse({
      password: newPassword,
    });
    if (!validationResult.success) {
      const currentLength = newPassword.length;
      const requiredLength = 6;
      const remainingChars = requiredLength - currentLength;

      const errorMessage = `Password is too short. Please add ${remainingChars} more character${
        remainingChars > 1 ? "s" : ""
      }. Minimum length is ${requiredLength} characters.`;
      console.log("Password validation failed:", errorMessage);

      return {
        success: false,
        error: errorMessage,
        validationError: true,
        details: {
          currentLength,
          requiredLength,
          remainingChars,
          isValid: false,
        },
      };
    }

    console.log("Password validation passed");

    const targetUser = await db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (!targetUser) {
      return {
        success: false,
        error: "User not found. Please check the user ID and try again.",
      };
    }

    const hashedPassword = await hash(newPassword, 10);

    // Update password
    await db
      .update(users)
      .set({ password: hashedPassword })
      .where(eq(users.id, userId));

    console.log("Password update successful", { userId });

    // Enhanced email template
    const emailTemplate = `
			<!DOCTYPE html>
			<html>
				<head>
					<style>
						body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
						.container { max-width: 600px; margin: 0 auto; padding: 20px; }
						.header { background-color: #f8f9fa; padding: 20px; text-align: center; }
						.content { padding: 20px; }
						.password { background-color: #f8f9fa; padding: 10px; margin: 10px 0; }
						.warning { color: #dc3545; }
					</style>
				</head>
				<body>
					<div class="container">
						<div class="header">
							<h1>Password Reset Notification</h1>
						</div>
						<div class="content">
							<p>Your password has been reset by an administrator.</p>
							<p>Your temporary password is:</p>
							<div class="password">
								<strong>${newPassword}</strong>
							</div>
							<p class="warning">
								For security reasons, please change your password immediately after logging in.
							</p>
							<p>If you did not request this password reset, please contact support immediately.</p>
						</div>
					</div>
				</body>
			</html>
		`;

    const emailResult = await sendEmail({
      to: targetUser.email,
      subject: "Important: Your Password Has Been Reset",
      html: emailTemplate,
    });

    console.log("Email sending result:", emailResult);

    if (!emailResult?.success) {
      console.error("Failed to send email:", emailResult.error);
      return {
        success: true,
        message: "Password reset successfully",
        warning:
          "Email notification could not be sent to the user. Please inform them manually.",
        userId: targetUser.id,
        email: targetUser.email,
      };
    }

    return {
      success: true,
      message:
        "Password has been reset successfully and notification email sent",
      userId: targetUser.id,
      email: targetUser.email,
    };
  } catch (error) {
    console.error("Error resetting password:", error);
    return {
      success: false,
      error:
        "An unexpected error occurred while resetting the password. Please try again later.",
    };
  }
}
