import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { messages, chats } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { nanoid } from "nanoid";
import Together from "together-ai";

const together = new Together({ 
    apiKey: process.env.TOGETHER_API_KEY
});

export async function POST(req: Request) {
    try {
        const session = await auth();
        if (!session?.user) {
            return new Response(JSON.stringify({ error: "Unauthorized" }), {
                status: 401,
            });
        }

        const { messages: messageHistory, chatId, model } = await req.json();
        const lastMessage = messageHistory[messageHistory.length - 1];
        const messageId = nanoid();
        const userMessageId = nanoid();

        // IMPORTANT: First check and create chat before any message operations
        const existingChat = await db.query.chats.findFirst({
            where: eq(chats.id, chatId),
        });

        if (!existingChat) {
            // Create chat first
            await db.insert(chats).values({
                id: chatId,
                userId: session.user.id,
                model: model || "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
                title: lastMessage.content.slice(0, 100),
                createdAt: new Date(),
            });
        }

        // Now safe to store the user's message as chat exists
        await db.insert(messages).values({
            id: userMessageId,
            chatId,
            role: "user", 
            content: lastMessage.content,
            createdAt: new Date(),
        });

        // Format messages for Together API
        const formattedMessages = messageHistory.map((msg: { role: string; content: string }) => ({
            role: msg.role,
            content: msg.content,
        }));

        try {
            // Create initial database entry
            await db.insert(messages).values({
                id: messageId,
                chatId,
                role: "assistant",
                content: "",
                createdAt: new Date(),
            });

            // Set up streaming response
            const encoder = new TextEncoder();
            let fullResponse = "";
            let lastChunkTime = Date.now();
            const TIMEOUT = 10000; // 10 seconds timeout

            const stream = new ReadableStream({
                async start(controller) {
                    try {
                        const completion = await together.chat.completions.create({
                            model: "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
                            messages: formattedMessages,
                            max_tokens: 8000,
                            temperature: 0.7,
                            top_p: 0.7,
                            top_k: 50,
                            repetition_penalty: 1,
                            stop: ["<|end▁of▁sentence|>"],
                            stream: true,
                        });

                        for await (const chunk of completion) {
                            const currentTime = Date.now();
                            if (currentTime - lastChunkTime > TIMEOUT) {
                                throw new Error("Stream timeout");
                            }
                            lastChunkTime = currentTime;

                            const text = chunk.choices[0]?.delta?.content || "";
                            if (text) {
                                fullResponse += text;

                                const chunkData = {
                                    id: messageId,
                                    role: "assistant",
                                    content: text,
                                };

                                controller.enqueue(
                                    encoder.encode(`data: ${JSON.stringify(chunkData)}\n\n`)
                                );
                            }

                            // Send a keep-alive comment every 5 seconds
                            if (currentTime - lastChunkTime > 5000) {
                                controller.enqueue(encoder.encode(`: ${Date.now()}\n\n`));
                                lastChunkTime = currentTime;
                            }
                        }

                        // Update final content in database
                        await db
                            .update(messages)
                            .set({ content: fullResponse })
                            .where(eq(messages.id, messageId));

                        controller.enqueue(encoder.encode("data: [DONE]\n\n"));
                    } catch (error) {
                        console.error("Stream processing error:", error);
                        // Try to save partial response if available
                        if (fullResponse) {
                            await db
                                .update(messages)
                                .set({ content: fullResponse })
                                .where(eq(messages.id, messageId));
                        }
                        throw error;
                    } finally {
                        controller.close();
                    }
                }
            });

            return new Response(stream, {
                headers: {
                    "Content-Type": "text/event-stream",
                    "Cache-Control": "no-cache, no-transform",
                    "Connection": "keep-alive",
                    "Transfer-Encoding": "chunked"
                },
            });
        } catch (error) {
            console.error("Together API error:", error);
            return new Response(
                JSON.stringify({
                    error: error instanceof Error ? error.message : "Failed to process message",
                }),
                { status: 500 }
            );
        }
    } catch (error) {
        console.error("[CHAT_ERROR]", error);
        return new Response(
            JSON.stringify({
                error: error instanceof Error ? error.message : "Internal server error",
            }),
            { status: 500 }
        );
    }
}