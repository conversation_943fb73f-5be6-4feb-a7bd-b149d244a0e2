import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { resetUserCreditsIfNeeded } from "@/lib/utils/credits";

export async function GET() {
  try {
    // Check if user is authenticated
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized - Please log in" },
        { status: 401 }
      );
    }

    // Check and reset user credits if needed (auto-reset)
    const creditReset = await resetUserCreditsIfNeeded(session.user.id);
    
    if (!creditReset.success) {
      return NextResponse.json(
        { error: "Failed to check credit status" },
        { status: 500 }
      );
    }

    // Get updated user details
    const user = await db.query.users.findFirst({
      where: (users, { eq }) => eq(users.id, session.user.id),
      columns: {
        credits: true,
        lastCreditReset: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Check if user needs reset (for UI indication)
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const lastReset = new Date(user.lastCreditReset);
    lastReset.setHours(0, 0, 0, 0);
    
    const needsReset = lastReset < today;

    return NextResponse.json({
      credits: user.credits,
      lastReset: user.lastCreditReset.toISOString(),
      needsReset,
      wasAutoReset: creditReset.wasReset,
    });

  } catch (error) {
    console.error("Failed to fetch user credits:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch credits",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
