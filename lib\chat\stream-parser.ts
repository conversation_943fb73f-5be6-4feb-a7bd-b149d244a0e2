export class StreamParser {
	private buffer: string = '';
	private decoder = new TextDecoder();

	parseChunk(chunk: Uint8Array): string {
		try {
			const text = this.decoder.decode(chunk);
			this.buffer += text;

			// Process the buffer in smaller chunks to avoid quota issues
			const chunks = this.buffer.split('\n');
			this.buffer = chunks.pop() || '';

			return chunks
				.map(chunk => this.parseSingleChunk(chunk))
				.filter(Boolean)
				.join('');
		} catch (error) {
			console.error('Error parsing chunk:', error);
			return '';
		}
	}

	private parseSingleChunk(chunk: string): string {
		try {
			if (!chunk.trim()) return '';

			// Handle thinking blocks
			if (chunk.includes('<think>')) {
				return '';
			}
			if (chunk.includes('</think>')) {
				return '';
			}

			// Clean up any XML-like tags
			return chunk.replace(/<[^>]*>/g, '');
		} catch (error) {
			console.error('Error parsing single chunk:', error);
			return '';
		}
	}

	flush(): string {
		const remaining = this.buffer;
		this.buffer = '';
		return this.parseSingleChunk(remaining);
	}
}

export const createStreamParser = () => new StreamParser();