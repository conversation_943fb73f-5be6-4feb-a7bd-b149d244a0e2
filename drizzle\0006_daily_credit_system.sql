-- Add lastCreditReset column to users table
ALTER TABLE "users" ADD COLUMN "lastCreditReset" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL;

-- Create daily_credit_resets table
CREATE TABLE IF NOT EXISTS "daily_credit_resets" (
	"id" text PRIMARY KEY NOT NULL,
	"resetDate" timestamp NOT NULL,
	"usersAffected" integer NOT NULL,
	"creditsAssigned" integer DEFAULT 10 NOT NULL,
	"status" text DEFAULT 'pending' NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"completedAt" timestamp,
	"errorMessage" text
);

-- Enable pg_cron extension (requires superuser privileges)
-- Note: This may need to be run manually by database administrator
-- For managed databases like Neon, Supabase, etc., check if pg_cron is available
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Create function to reset daily credits
CREATE OR REPLACE FUNCTION reset_daily_credits()
RETURNS TABLE(
    users_affected INTEGER,
    credits_assigned INTEGER,
    reset_id TEXT,
    success BOOLEAN,
    error_message TEXT
) AS $$
DECLARE
    reset_id_var TEXT;
    users_count INTEGER := 0;
    credits_amount INTEGER := 10;
    error_msg TEXT := NULL;
BEGIN
    -- Generate unique ID for this reset operation
    reset_id_var := 'reset_' || extract(epoch from now())::text || '_' || floor(random() * 1000)::text;

    -- Start transaction and log the reset operation
    INSERT INTO daily_credit_resets (
        id,
        resetDate,
        usersAffected,
        creditsAssigned,
        status
    ) VALUES (
        reset_id_var,
        now(),
        0,
        credits_amount,
        'pending'
    );

    BEGIN
        -- Update users who haven't been reset today
        -- Only reset users where lastCreditReset is before today (UTC)
        UPDATE users
        SET
            credits = credits_amount,
            lastCreditReset = now(),
            updatedAt = now()
        WHERE DATE(lastCreditReset AT TIME ZONE 'UTC') < DATE(now() AT TIME ZONE 'UTC');

        -- Get count of affected users
        GET DIAGNOSTICS users_count = ROW_COUNT;

        -- Mark reset as completed
        UPDATE daily_credit_resets
        SET
            status = 'completed',
            usersAffected = users_count,
            completedAt = now()
        WHERE id = reset_id_var;

        -- Return success result
        RETURN QUERY SELECT
            users_count,
            credits_amount,
            reset_id_var,
            true,
            error_msg;

    EXCEPTION WHEN OTHERS THEN
        -- Handle any errors
        error_msg := SQLERRM;

        -- Mark reset as failed
        UPDATE daily_credit_resets
        SET
            status = 'failed',
            errorMessage = error_msg,
            completedAt = now()
        WHERE id = reset_id_var;

        -- Return error result
        RETURN QUERY SELECT
            0,
            credits_amount,
            reset_id_var,
            false,
            error_msg;
    END;
END;
$$ LANGUAGE plpgsql;

-- Create a function to manually trigger credit reset (for admin/testing)
CREATE OR REPLACE FUNCTION manual_reset_daily_credits()
RETURNS TABLE(
    users_affected INTEGER,
    credits_assigned INTEGER,
    reset_id TEXT,
    success BOOLEAN,
    error_message TEXT
) AS $$
BEGIN
    -- Simply call the main reset function
    RETURN QUERY SELECT * FROM reset_daily_credits();
END;
$$ LANGUAGE plpgsql;

-- Function to check if pg_cron is available and schedule the job
CREATE OR REPLACE FUNCTION setup_daily_credit_cron()
RETURNS TEXT AS $$
DECLARE
    cron_available BOOLEAN := FALSE;
    result_msg TEXT;
BEGIN
    -- Check if pg_cron extension is available
    SELECT EXISTS(
        SELECT 1 FROM pg_extension WHERE extname = 'pg_cron'
    ) INTO cron_available;

    IF cron_available THEN
        -- Schedule the daily credit reset to run every day at midnight UTC
        -- This will run the function daily at 00:00 UTC
        PERFORM cron.schedule(
            'daily-credit-reset',
            '0 0 * * *',
            'SELECT reset_daily_credits();'
        );
        result_msg := 'Daily credit reset cron job scheduled successfully at midnight UTC';
    ELSE
        result_msg := 'pg_cron extension not available. Please enable it or use alternative scheduling method.';
    END IF;

    RETURN result_msg;
END;
$$ LANGUAGE plpgsql;

-- Function to unschedule the cron job (for cleanup)
CREATE OR REPLACE FUNCTION remove_daily_credit_cron()
RETURNS TEXT AS $$
DECLARE
    cron_available BOOLEAN := FALSE;
    result_msg TEXT;
BEGIN
    -- Check if pg_cron extension is available
    SELECT EXISTS(
        SELECT 1 FROM pg_extension WHERE extname = 'pg_cron'
    ) INTO cron_available;

    IF cron_available THEN
        -- Remove the scheduled job
        PERFORM cron.unschedule('daily-credit-reset');
        result_msg := 'Daily credit reset cron job removed successfully';
    ELSE
        result_msg := 'pg_cron extension not available';
    END IF;

    RETURN result_msg;
END;
$$ LANGUAGE plpgsql;

-- Try to setup the cron job (this will work if pg_cron is available)
-- If it fails, you can run this manually later: SELECT setup_daily_credit_cron();
DO $$
BEGIN
    PERFORM setup_daily_credit_cron();
EXCEPTION WHEN OTHERS THEN
    -- If pg_cron is not available, just continue
    RAISE NOTICE 'Could not setup cron job automatically. Run SELECT setup_daily_credit_cron(); manually if pg_cron is available.';
END;
$$;
