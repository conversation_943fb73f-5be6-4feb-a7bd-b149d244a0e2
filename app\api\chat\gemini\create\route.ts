import { chats } from "@/lib/db/schema";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";

export async function POST(req: Request) {
  try {
    const session = await auth();
    if (!session?.user) {
      return new Response("Unauthorized", { status: 401 });
    }

    const { id, title } = await req.json();

    const result = await db
      .insert(chats)
      .values({
        id,
        userId: session.user.id,
        title,
        model: "gemini-pro", // Set default model
      })
      .returning();

    return new Response(JSON.stringify(result[0]), {
      headers: { "Content-Type": "application/json" },
    });
  } catch (error) {
    console.error("[CREATE_CHAT_ERROR]", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}
