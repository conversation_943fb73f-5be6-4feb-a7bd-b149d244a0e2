import { GoogleGenerativeA<PERSON> } from "@google/generative-ai";
import { NextResponse } from "next/server";

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);

function cleanJsonResponse(text: string): string {
  // Remove markdown code blocks if present
  text = text.replace(/```json\n/g, "").replace(/```/g, "");
  // Remove any leading/trailing whitespace
  text = text.trim();
  return text;
}

export async function POST(req: Request) {
  try {
    const { url, keywords } = await req.json();

    const model = genAI.getGenerativeModel({ model: "gemini-2.5-pro" });

    const prompt = `Generate a list of high-quality backlink opportunities for the following website and keywords. Return the response in this exact JSON format without any markdown formatting or additional text:
		[
			{
				"platform": "platform name",
				"link": "platform URL",
				"instructions": "step-by-step instructions to create the backlink"
			}
		]

		Website URL: ${url}
		Target Keywords: ${keywords}`;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    // Clean and parse the response
    const cleanedText = cleanJsonResponse(text);
    const jsonResult = JSON.parse(cleanedText);

    return NextResponse.json({ result: jsonResult });
  } catch (error) {
    console.error("Backlink maker error:", error);
    return NextResponse.json(
      { error: "Failed to generate backlinks" },
      { status: 500 }
    );
  }
}
