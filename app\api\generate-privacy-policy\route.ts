import { NextResponse } from "next/server";

interface PolicyFormData {
	companyName: string;
	website: string;
	email: string;
	collectsPersonalData: boolean;
	usesAnalytics: boolean;
	usesCookies: boolean;
}

export async function POST(request: Request) {
	try {
		const formData: PolicyFormData = await request.json();

		const policy = generatePrivacyPolicy(formData);
		return NextResponse.json({ policy });
	} catch (error) {
        console.log(error);
		return NextResponse.json(
			{ error: "Failed to generate privacy policy" },
			{ status: 500 }
		);
	}
}

function generatePrivacyPolicy(data: PolicyFormData): string {
	const currentDate = new Date().toLocaleDateString();
	
	let policy = `Privacy Policy for ${data.companyName}

Last updated: ${currentDate}

1. Introduction
Welcome to ${data.companyName}. We are committed to protecting your personal information and your right to privacy.

2. Contact Information
If you have any questions about this privacy policy, please contact us at:
Email: ${data.email}
Website: ${data.website}

`;

	if (data.collectsPersonalData) {
		policy += `3. Information We Collect
We collect personal information that you voluntarily provide to us when you use our services.

`;
	}

	if (data.usesAnalytics) {
		policy += `4. Analytics
We use analytics tools to help us understand how our visitors use the website. This helps us improve our services and user experience.

`;
	}

	if (data.usesCookies) {
		policy += `5. Cookies
We use cookies and similar tracking technologies to track activity on our website and hold certain information.

`;
	}

	policy += `6. Changes to This Privacy Policy
We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page.

7. Your Rights
You have the right to access, update, or delete your personal information at any time.`;

	return policy;
}