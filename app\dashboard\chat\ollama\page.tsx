"use client";

import { nanoid } from "nanoid";
import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { Bot, Loader2 } from "lucide-react";

export default function GeminiChatPage() {
	const router = useRouter();

	useEffect(() => {
		const tempChatId = nanoid();
		router.replace(`/dashboard/chat/ollama/${tempChatId}`);
	}, [router]);

	return (
		<div className="flex h-[calc(100vh-4rem)] flex-col items-center justify-center gap-2">
			<Bot className="h-12 w-12 text-muted-foreground" />
			<h3 className="text-xl font-semibold">Starting Ollama Chat</h3>
			<div className="flex items-center gap-2">
				<Loader2 className="h-4 w-4 animate-spin" />
				<p className="text-sm text-muted-foreground">Creating new chat...</p>
			</div>
		</div>
	);
}