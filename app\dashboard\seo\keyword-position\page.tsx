"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Loader2 } from "lucide-react";

export default function KeywordPositionPage() {
	const [url, setUrl] = useState("");
	const [keyword, setKeyword] = useState("");
	const [loading, setLoading] = useState(false);
	const [result, setResult] = useState<{
		position: number;
		searchVolume: number;
		difficulty: number;
		recommendations: string[];
	} | null>(null);
	const { toast } = useToast();

	const handleCheck = async () => {
		if (!url.trim() || !keyword.trim()) {
			toast({
				title: "Error",
				description: "Please fill in all fields",
				variant: "destructive",
			});
			return;
		}

		try {
			setLoading(true);
			const response = await fetch("/api/seo/keyword-position", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ url, keyword }),
			});

			if (!response.ok) {
				throw new Error("Failed to check keyword position");
			}

			const data = await response.json();
			setResult(data.result);
		} catch (error) {
            console.error("An error occurred:", error);
			toast({
				title: "Error",
				description: "Failed to check keyword position. Please try again.",
				variant: "destructive",
			});
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="container mx-auto py-8">
			<h1 className="text-3xl font-bold mb-6">Keyword Position Checker</h1>
			<div className="grid gap-6">
				<div>
					<label className="block mb-2">Website URL</label>
					<Input
						placeholder="Enter your website URL"
						value={url}
						onChange={(e) => setUrl(e.target.value)}
					/>
				</div>
				<div>
					<label className="block mb-2">Keyword</label>
					<Input
						placeholder="Enter the keyword to check"
						value={keyword}
						onChange={(e) => setKeyword(e.target.value)}
					/>
				</div>
				<Button 
					onClick={handleCheck} 
					disabled={loading}
					className="w-full md:w-auto"
				>
					{loading ? (
						<>
							<Loader2 className="mr-2 h-4 w-4 animate-spin" />
							Checking...
						</>
					) : (
						"Check Position"
					)}
				</Button>
				{result && (
					<div className="space-y-4">
						<div className="p-4 border rounded-lg">
							<h2 className="text-xl font-semibold mb-4">Results</h2>
							<div className="grid gap-4">
								<div>
									<p className="text-lg">
										Position: <span className="font-semibold">#{result.position}</span>
									</p>
									<p>
										Monthly Search Volume: <span className="font-semibold">{result.searchVolume}</span>
									</p>
									<p>
										Keyword Difficulty: <span className="font-semibold">{result.difficulty}%</span>
									</p>
								</div>
								<div>
									<h3 className="font-medium mb-2">Recommendations</h3>
									<ul className="list-disc pl-5 space-y-2">
										{result.recommendations.map((recommendation, index) => (
											<li key={index} className="text-muted-foreground">
												{recommendation}
											</li>
										))}
									</ul>
								</div>
							</div>
						</div>
					</div>
				)}
			</div>
		</div>
	);
}