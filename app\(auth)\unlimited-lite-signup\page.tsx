"use client";

import { SignUpForm } from "@/components/forms/signup-form";
import Link from "next/link";
import { AuthLayout } from "@/components/auth-layout";

export default function UnlimitedLiteSignUpPage() {
  return (
    <AuthLayout centerOnly>
      <div className="text-center">
        <h1 className="text-3xl font-bold gradient-text mb-2">
          Create Account
        </h1>
        <p className="text-muted-foreground">
          Sign up for an Unlimited Lite account
        </p>
      </div>

      <div className="mt-8">
        <SignUpForm userType="unlimited-lite" />
      </div>

      <p className="mt-8 text-center text-sm text-muted-foreground">
        Already have an account?{" "}
        <Link
          href="/login"
          className="font-medium bg-gradient-to-r from-blue-500 to-purple-500 bg-clip-text text-transparent hover:from-blue-600 hover:to-purple-600 transition-all duration-200"
        >
          Sign in
        </Link>
      </p>
    </AuthLayout>
  );
}
