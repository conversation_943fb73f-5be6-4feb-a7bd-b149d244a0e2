"use client";

import { ChatContainer } from "@/components/chat/chat-container";
import { useParams } from "next/navigation";

export default function LLaMAchatWithId() {
  const params = useParams();
  const chatId = params?.chatId as string;

  return (
    <div className="flex-1 space-y-6">
      <div className="container h-[calc(100vh-4rem)]">
        <ChatContainer id={chatId} modelName="LLaMA 3.3" apiModel="llama33" />
      </div>
    </div>
  );
}
