"use client";

import { cn } from "@/lib/utils";
import { Message } from "ai";
import { Bot, User2 } from "lucide-react";
import React, { ComponentPropsWithoutRef, forwardRef } from "react";
import Markdown, { Components } from "react-markdown";
import { PrismLight as <PERSON>ynta<PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-syntax-highlighter";
import { vscDarkPlus } from "react-syntax-highlighter/dist/cjs/styles/prism";

type CodeComponent = NonNullable<Components["code"]>;

interface ChatMessageProps {
  message: Message;
}

export function ChatMessage({ message }: ChatMessageProps) {
  const isUser = message.role === "user";

  const components: Components = {
    p: ({ children, ...props }) => {
      const hasPreElement = React.Children.toArray(children).some(
        (child) => React.isValidElement(child) && child.type === "pre"
      );

      if (hasPreElement) {
        return <>{children}</>;
      }

      return (
        <p className="mb-2 last:mb-0 leading-relaxed" {...props}>
          {children}
        </p>
      );
    },

    code: forwardRef<
      HTMLElement,
      ComponentPropsWithoutRef<CodeComponent> & {
        inline?: boolean;
        className?: string;
        children?: React.ReactNode;
      }
    >(({ inline, className, children, ...props }, ref) => {
      const match = /language-(\w+)/.exec(className || "");
      const language = match ? match[1] : "";

      if (!inline && language) {
        return (
          <SyntaxHighlighter
            language={language}
            // @ts-expect-error - This is a known issue with the SyntaxHighlighter component
            style={vscDarkPlus}
            PreTag="div"
            customStyle={
              {
                margin: "1rem 0",
                padding: "1rem",
                borderRadius: "0.5rem",
                backgroundColor: "rgb(30, 30, 30)",
              } as React.CSSProperties
            }
            className={cn(
              "mb-2 rounded-lg overflow-x-auto shadow-lg",
              "border border-gray-800",
              className
            )}
            {...props}
          >
            {String(children).replace(/\n$/, "")}
          </SyntaxHighlighter>
        );
      }

      return (
        <code
          className={cn(
            "rounded px-1.5 py-0.5 bg-muted font-mono text-sm",
            className
          )}
          {...props}
          ref={ref}
        >
          {children}
        </code>
      );
    }),
  };

  // @ts-expect-error - This is a known issue with the SyntaxHighlighter component
  components.code.displayName = "CodeComponent";

  return (
    <div
      className={cn(
        "flex w-full items-start gap-2 sm:gap-4 p-2 sm:p-4 transition-colors duration-200",
        isUser ? "bg-muted/50 dark:bg-muted/80" : "bg-background dark:bg-muted/30"
      )}
    >
      <div
        className={cn(
          "flex h-6 w-6 sm:h-8 sm:w-8 shrink-0 select-none items-center justify-center rounded-md border shadow-sm",
          isUser ? "bg-background dark:bg-muted" : "bg-primary dark:bg-primary/80"
        )}
      >
        {isUser ? (
          <User2 className="h-3 w-3 sm:h-4 sm:w-4 text-foreground dark:text-foreground" />
        ) : (
          <Bot className="h-3 w-3 sm:h-4 sm:w-4 text-background dark:text-background" />
        )}
      </div>
      <div className="flex-1 space-y-2 overflow-hidden">
        <Markdown
          components={components}
          className={cn(
            "prose break-words dark:prose-invert prose-p:leading-relaxed prose-pre:p-0",
            "prose-headings:mb-4 prose-headings:mt-6",
            "prose-code:rounded-sm prose-code:bg-muted/50 dark:prose-code:bg-muted prose-code:px-1 prose-code:py-0.5",
            "prose-hr:my-4 prose-hr:border-border",
            "prose-img:rounded-md prose-img:border prose-img:border-border",
            "prose-blockquote:border-l-4 prose-blockquote:border-border prose-blockquote:pl-4",
            "prose-ul:my-4 prose-ol:my-4",
            "prose-li:my-2",
            "prose-table:my-4 prose-thead:border-b prose-thead:border-border",
            "prose-tr:border-b prose-tr:border-border",
            "prose-th:px-3 prose-th:py-2 prose-th:text-left",
            "prose-td:px-3 prose-td:py-2",
            "text-sm sm:text-base max-w-full overflow-x-auto",
            "dark:text-foreground/90"
          )}

      >
          {message.content}
        </Markdown>
      </div>
    </div>
  );
}
