import { GoogleGenerativeA<PERSON> } from "@google/generative-ai";
import { NextResponse } from "next/server";

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);

function cleanJsonResponse(text: string): string {
  text = text.replace(/```json\n/g, "").replace(/```/g, "");
  return text.trim();
}

export async function POST(req: Request) {
  try {
    const { url } = await req.json();

    const model = genAI.getGenerativeModel({ model: "gemini-2.5-pro" });

    const prompt = `Analyze the backlink profile for the following website and provide detailed information. Return the response in this exact JSON format without any markdown formatting or additional text:
		{
			"totalBacklinks": number,
			"domainAuthority": number (0-100),
			"backlinks": [
				{
					"url": "linking page URL",
					"anchorText": "anchor text used",
					"domainAuthority": number (0-100),
					"dofollow": boolean
				}
			]
		}

		Website URL: ${url}

		Provide realistic sample data for demonstration purposes.`;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    const cleanedText = cleanJsonResponse(text);
    const jsonResult = JSON.parse(cleanedText);

    return NextResponse.json({ result: jsonResult });
  } catch (error) {
    console.error("Backlink checker error:", error);
    return NextResponse.json(
      { error: "Failed to check backlinks" },
      { status: 500 }
    );
  }
}
