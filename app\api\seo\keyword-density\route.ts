import { GoogleGenerativeAI } from "@google/generative-ai";
import { NextResponse } from "next/server";

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);

function cleanJsonResponse(text: string): string {
	text = text.replace(/```json\n|```/g, '').trim();
	return text;
}

export async function POST(req: Request) {
	try {
		const { content } = await req.json();
		
		const model = genAI.getGenerativeModel({ model: "gemini-pro" });
		
		const prompt = `Analyze the following content for keyword density and provide insights. Return the response in this exact JSON format without any markdown:
		{
			"totalWords": number,
			"keywords": [
				{
					"keyword": "string",
					"count": number,
					"density": number (percentage),
					"prominence": "high" | "medium" | "low"
				}
			],
			"suggestions": [
				"suggestion1",
				"suggestion2"
			]
		}

		Content to analyze:
		${content}`;

		const result = await model.generateContent(prompt);
		const response = await result.response;
		const text = response.text();
		
		const cleanedText = cleanJsonResponse(text);
		const jsonResult = JSON.parse(cleanedText);

		return NextResponse.json({ result: jsonResult });
	} catch (error) {
		console.error("Keyword density analysis error:", error);
		return NextResponse.json(
			{ error: "Failed to analyze keyword density" },
			{ status: 500 }
		);
	}
}