"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Download } from "lucide-react";
import Image from "next/image";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";

interface ImageResult {
	id: number;
	thumbnail: string;
	preview: string;
	large: string;
	width: number;
	height: number;
	tags: string;
	user: string;
	userImage: string;
	downloads: number;
	likes: number;
}

export default function ImageSearchPage() {
	const [query, setQuery] = useState("");
	const [loading, setLoading] = useState(false);
	const [imageType, setImageType] = useState("all");
	const [orientation, setOrientation] = useState("all");
	const [results, setResults] = useState<ImageResult[]>([]);
	const { toast } = useToast();

	const handleSearch = async () => {
		if (!query.trim()) {
			toast({
				title: "Error",
				description: "Please enter a search term",
				variant: "destructive",
			});
			return;
		}

		try {
			setLoading(true);
			const params = new URLSearchParams({
				q: query,
				image_type: imageType,
				orientation: orientation,
				per_page: "30",
			});

			const response = await fetch(`/api/image/search?${params}`);
			if (!response.ok) throw new Error("Failed to search images");

			const data = await response.json();
			setResults(data.images);
		} catch (error) {
            console.error("An error occurred:", error);
			toast({
				title: "Error",
				description: "Failed to search images. Please try again.",
				variant: "destructive",
			});
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="container mx-auto py-8">
			<h1 className="text-3xl font-bold mb-6">Image Search</h1>
			<div className="grid gap-6">
				<div className="flex flex-col md:flex-row gap-4">
					<Input
						placeholder="Enter search term..."
						value={query}
						onChange={(e) => setQuery(e.target.value)}
						className="flex-1"
					/>
					<Select value={imageType} onValueChange={setImageType}>
						<SelectTrigger className="w-[180px]">
							<SelectValue placeholder="Image type" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="all">All</SelectItem>
							<SelectItem value="photo">Photo</SelectItem>
							<SelectItem value="illustration">Illustration</SelectItem>
							<SelectItem value="vector">Vector</SelectItem>
						</SelectContent>
					</Select>
					<Select value={orientation} onValueChange={setOrientation}>
						<SelectTrigger className="w-[180px]">
							<SelectValue placeholder="Orientation" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="all">All</SelectItem>
							<SelectItem value="horizontal">Horizontal</SelectItem>
							<SelectItem value="vertical">Vertical</SelectItem>
						</SelectContent>
					</Select>
					<Button onClick={handleSearch} disabled={loading}>
						{loading ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								Searching...
							</>
						) : (
							"Search Images"
						)}
					</Button>
				</div>

				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
					{results.map((image) => (
						<div key={image.id} className="border rounded-lg overflow-hidden">
							<div className="relative aspect-video">
								<Image
									src={image.preview}
									alt={image.tags}
									fill
									className="object-cover"
								/>
							</div>
							<div className="p-4">
								<p className="text-sm text-muted-foreground mb-2">{image.tags}</p>
								<div className="flex items-center justify-between">
									<div className="flex items-center gap-2">
										<Image
											src={image.userImage}
											alt={image.user}
											width={24}
											height={24}
											className="rounded-full"
										/>
										<span className="text-sm">{image.user}</span>
									</div>
									<a
										href={image.large}
										download
										target="_blank"
										rel="noopener noreferrer"
										className="text-primary hover:text-primary/80"
									>
										<Download className="h-5 w-5" />
									</a>
								</div>
							</div>
						</div>
					))}
				</div>
			</div>
		</div>
	);
}