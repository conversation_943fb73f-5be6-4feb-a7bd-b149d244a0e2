import { auth } from "@/lib/auth";
import { NextResponse } from "next/server";
import Together from "together-ai";

const together = new Together({
  apiKey: process.env.TOGETHER_API_KEY,
});

export async function POST(request: Request) {
  try {
    // Get user session
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse request body
    const { prompt, model, aspectRatio = "1:1" } = await request.json();

    if (!prompt || prompt.trim() === "") {
      return NextResponse.json(
        { error: "Prompt is required" },
        { status: 400 }
      );
    }

    // Clean and enhance the prompt based on model
    const cleanPrompt = prompt.trim();
    let enhancedPrompt = cleanPrompt;

    switch (model) {
      case "grok":
        enhancedPrompt = `${cleanPrompt}, creative AI generated style, vibrant colors`;
        break;
      case "midjourney":
        enhancedPrompt = `${cleanPrompt}, artistic illustration, detailed, high quality`;
        break;
      case "dalle":
        enhancedPrompt = `${cleanPrompt}, photorealistic, high detail, professional`;
        break;
      case "flux":
        enhancedPrompt = `${cleanPrompt}, high quality, detailed, sharp focus`;
        break;
      case "gemini":
        enhancedPrompt = `${cleanPrompt}, multimodal AI generated, balanced composition`;
        break;
      default:
        enhancedPrompt = `${cleanPrompt}, high quality AI generated image`;
    }

    // Calculate dimensions based on aspect ratio
    let width = 1024;
    let height = 1024;

    switch (aspectRatio) {
      case "16:9":
        width = 1024;
        height = 576;
        break;
      case "4:3":
        width = 1024;
        height = 768;
        break;
      case "3:4":
        width = 768;
        height = 1024;
        break;
      case "9:16":
        width = 576;
        height = 1024;
        break;
      default: // 1:1
        width = 1024;
        height = 1024;
    }

    console.log(
      `Generating image with Together AI for ${model || "default"}:`,
      {
        prompt: enhancedPrompt,
        dimensions: `${width}x${height}`,
        aspectRatio,
      }
    );

    // Generate image using Together AI
    const response = await together.images.create({
      model: "black-forest-labs/FLUX.1-schnell-Free",
      prompt: enhancedPrompt,
      width: width,
      height: height,
    });

    // Get image URL from response
    const imageUrl = response.data[0]?.url;

    if (!imageUrl) {
      return NextResponse.json(
        {
          error: "Failed to generate image - no URL returned",
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      imageUrl: imageUrl,
      model: "FLUX.1-schnell-Free",
      dimensions: { width, height },
      aspectRatio,
    });
  } catch (error: unknown) {
    console.error("Error generating image:", error);
    const errorMessage =
      error instanceof Error ? error.message : "Failed to generate image";
    return NextResponse.json(
      {
        error: errorMessage,
      },
      { status: 500 }
    );
  }
}
