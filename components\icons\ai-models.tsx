interface IconProps {
	className?: string;
}

export const AIModelIcons = {
	DeepSeek: ({ className }: IconProps) => (
		<svg className={className} viewBox="0 0 24 24" fill="currentColor" height="24" width="24">
			<path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z" />
		</svg>
	),
	GPT4: ({ className }: IconProps) => (
		<svg className={className} viewBox="0 0 24 24" fill="currentColor" height="24" width="24">
			<path d="M22.282 9.821a5.985 5.985 0 0 0-.516-4.91 6.046 6.046 0 0 0-6.51-2.9A6.065 6.065 0 0 0 4.981 4.18a5.985 5.985 0 0 0-3.998 2.9 6.046 6.046 0 0 0 .743 7.097 5.98 5.98 0 0 0 .51 4.911 6.051 6.051 0 0 0 6.515 2.9A5.985 5.985 0 0 0 13.26 24a6.056 6.056 0 0 0 5.772-4.206 5.99 5.99 0 0 0 3.997-2.9 6.056 6.056 0 0 0-.747-7.073zM13.26 22.43a4.476 4.476 0 0 1-2.876-1.04l.141-.081 4.779-2.758a.795.795 0 0 0 .392-.681v-6.737l2.02 1.168a.071.071 0 0 1 .038.052v5.583a4.504 4.504 0 0 1-4.494 4.494zM3.6 18.304a4.47 4.47 0 0 1-.535-3.014l.142.085 4.783 2.759a.78.78 0 0 0 .78 0l5.843-3.369v2.332a.074.074 0 0 1-.028.057l-4.832 2.795a4.504 4.504 0 0 1-6.153-1.645zm-.617-8.908a4.476 4.476 0 0 1 2.34-2.097v.076l-.004 5.517a.787.787 0 0 0 .391.681l5.843 3.369-2.02 1.168a.074.074 0 0 1-.066.004l-4.831-2.795a4.504 4.504 0 0 1-1.653-5.923zm16.556 3.009a4.47 4.47 0 0 1-1.85 2.107v-.076l.004-5.517a.787.787 0 0 0-.391-.681l-5.843-3.369 2.02-1.168a.074.074 0 0 1 .066-.004l4.831 2.795a4.504 4.504 0 0 1 1.163 5.913zm-1.195-6.745l-.142-.085-4.779-2.759a.78.78 0 0 0-.78 0L6.8 6.181V3.849a.074.074 0 0 1 .028-.057l4.832-2.795a4.504 4.504 0 0 1 6.684 4.659zm-12.638.811l-2.02-1.168a.071.071 0 0 1-.038-.052V3.664a4.504 4.504 0 0 1 7.37-3.454l-.141.081-4.779 2.758a.795.795 0 0 0-.392.681v6.737zm.952 4.879l-2.588-1.49v-2.974l2.588 1.49v2.974zm.784-3.003l2.588-1.49 2.588 1.49v2.974l-2.588 1.49-2.588-1.49v-2.974zm5.96 3.003v-2.974l2.588-1.49v2.974l-2.588 1.49z" />
		</svg>
	),
	Claude: ({ className }: IconProps) => (
		<svg className={className} viewBox="0 0 24 24" fill="currentColor" height="24" width="24">
			<path d="M12 2L1 12l11 10 11-10L12 2zm0 4.828L19.172 12 12 17.172 4.828 12 12 6.828z" />
		</svg>
	),
	Gemini: ({ className }: IconProps) => (
		<svg className={className} viewBox="0 0 24 24" fill="currentColor" height="24" width="24">
			<path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-12h2v6h-2zm0 8h2v2h-2z" />
		</svg>
	),
	Ollama: ({ className }: IconProps) => (
		<svg className={className} viewBox="0 0 24 24" fill="currentColor" height="24" width="24">
			<path d="M17.001 20h-10c-1.654 0-3-1.346-3-3v-10c0-1.654 1.346-3 3-3h10c1.654 0 3 1.346 3 3v10c0 1.654-1.346 3-3 3zm0-14h-10c-.551 0-1 .449-1 1v10c0 .551.449 1 1 1h10c.551 0 1-.449 1-1v-10c0-.551-.449-1-1-1z" />
		</svg>
	),

};
