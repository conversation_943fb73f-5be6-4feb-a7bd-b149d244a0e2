"use client";

import { useToast } from "@/hooks/use-toast";
import { useChatStore } from "@/lib/store/chat-store";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useChat } from "ai/react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Bo<PERSON>, Loader2, Send } from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useMemo, useRef } from "react";
import { Alert, AlertDescription } from "../ui/alert";
import { Button } from "../ui/button";
import { Card } from "../ui/card";
import { ScrollArea } from "../ui/scroll-area";
import { Textarea } from "../ui/textarea";
import { ChatMessage } from "./chat-message";

interface Message {
  id: string;
  role: "user" | "assistant";
  content: string;
  createdAt: Date;
}

interface ChatContainerProps {
  id?: string;
  modelName: string;
  apiModel?: string;
}

export function ChatContainer({
  id,
  modelName,
  apiModel = "gemini-1.5-pro",
}: ChatContainerProps) {
  const params = useParams();
  const chatId = id || (params?.chatId as string);
  const router = useRouter();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const {} = useChatStore();

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get messages from the database
  const {
    data: dbMessages,
    isLoading: isLoadingMessages,
    error: loadError,
  } = useQuery<Message[], Error>({
    queryKey: ["messages", chatId],
    queryFn: async () => {
      const response = await fetch(`/api/chat/messages?chatId=${chatId}`);
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to load messages");
      }
      const data = await response.json();
      return data.map(
        (msg: {
          id: string;
          role: string;
          content: string;
          createdAt: string;
        }) => ({
          id: msg.id,
          role: msg.role as "user" | "assistant",
          content: msg.content,
          createdAt: new Date(msg.createdAt),
        })
      );
    },
  });

  // Determine API endpoint based on model
  const apiEndpoint = useMemo(() => {
    switch (apiModel) {
      case "gemini-1.5-pro":
        return "/api/chat/gemini/message";
      case "deepseekv3":
        return "/api/chat/deepseekv3/message";
      case "openrouter":
        return "/api/chat/openrouter/message";
      case "llama33":
        return "/api/chat/llama33/message";
      default:
        return "/api/chat/deepseekv3/message";
    }
  }, [apiModel]);

  // Initialize useChat with database messages
  const {
    messages: streamingMessages,
    input,
    handleInputChange,
    handleSubmit: aiSubmit,
    isLoading,
    setMessages: setStreamingMessages,
  } = useChat({
    api: apiEndpoint,
    id: chatId,
    initialMessages: dbMessages || [],
    body: {
      chatId,
      model: modelName.toLowerCase().replace(/[^a-z0-9]/g, ""), // Convert model name to API format
    },
    onResponse: async (response) => {
      console.log("AI Response status:", response.status);
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || "Failed to send message");
      }

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (!reader) return;

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split("\n");

          for (const line of lines) {
            if (line.startsWith("data: ")) {
              const data = line.slice(5).trim();
              if (data === "[DONE]") continue;

              try {
                const parsed = JSON.parse(data);
                // Update the streaming messages with the new chunk
                setStreamingMessages((prev) => {
                  const lastMessage = prev[prev.length - 1];
                  if (lastMessage?.role === "assistant") {
                    // Update the last assistant message
                    return prev.map((msg, i) =>
                      i === prev.length - 1
                        ? { ...msg, content: msg.content + parsed.content }
                        : msg
                    );
                  } else {
                    // Add a new assistant message
                    return [
                      ...prev,
                      {
                        id: parsed.id,
                        role: "assistant",
                        content: parsed.content,
                      },
                    ];
                  }
                });
              } catch (e) {
                if (data !== "[DONE]") {
                  console.log("Parse chunk error:", e);
                }
              }
            }
          }
        }
      } catch (error) {
        console.error("Stream reading error:", error);
        throw error;
      } finally {
        reader.releaseLock();
      }
    },
    onFinish: async () => {
      console.log("Stream finished");
      await queryClient.invalidateQueries({ queryKey: ["messages", chatId] });
    },
    onError: (error) => {
      console.error("AI Chat error details:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to send message",
      });

      if (error.message?.includes("Unauthorized")) {
        router.push("/auth/signin");
      }
    },
  });

  // Update streaming messages when database messages change
  useEffect(() => {
    if (dbMessages?.length && !streamingMessages.length) {
      setStreamingMessages(
        dbMessages.map((msg) => ({
          id: msg.id,
          role: msg.role,
          content: msg.content,
        }))
      );
    }
  }, [dbMessages, streamingMessages.length, setStreamingMessages]);

  // Prevent duplicate submissions
  const isSubmitting = useRef(false);

  // Custom submit handler
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (isLoading || !input.trim() || isSubmitting.current) return;

    isSubmitting.current = true;
    try {
      await aiSubmit(e);
    } catch (error) {
      console.error("Submit error:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to send message. Please try again.",
      });
    } finally {
      isSubmitting.current = false;
    }
  };

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [streamingMessages]);

  // Add keydown handler for textarea
  const handleKeyDown = async (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      if (isLoading || !input.trim() || isSubmitting.current) return;

      isSubmitting.current = true;
      try {
        // Create a synthetic form event and call aiSubmit directly
        const syntheticEvent = {
          preventDefault: () => {},
          currentTarget: e.currentTarget.form,
        } as React.FormEvent<HTMLFormElement>;
        await aiSubmit(syntheticEvent);
      } catch (error) {
        console.error("Submit error:", error);
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to send message. Please try again.",
        });
      } finally {
        isSubmitting.current = false;
      }
    }
  };

  // Use streaming messages for display
  const displayMessages = useMemo(() => {
    return streamingMessages;
  }, [streamingMessages]);

  return (
    <div className="flex h-full flex-col">
      <Card className="rounded-none border-x-0 border-t-0 bg-background/95 dark:bg-background/95">
        <div className="flex items-center justify-between border-b px-2 sm:px-4 py-2 sm:py-3 dark:border-border">
          <div className="flex items-center gap-2">
            <Bot className="h-4 w-4 sm:h-5 sm:w-5 text-primary dark:text-primary" />
            <h2 className="text-base sm:text-lg font-semibold tracking-tight text-foreground dark:text-foreground">
              Chat with {modelName}
            </h2>
          </div>
        </div>
      </Card>

      <ScrollArea className="flex-1 px-2 sm:px-4">
        <div className="space-y-3 sm:space-y-4 py-2 sm:py-4">
          {loadError ? (
            <Alert variant="destructive" className="mx-2">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="text-sm">
                {loadError.message}
              </AlertDescription>
            </Alert>
          ) : isLoadingMessages ? (
            <div className="flex h-[60vh] items-center justify-center">
              <Loader2 className="h-6 w-6 sm:h-8 sm:w-8 animate-spin text-muted-foreground dark:text-muted-foreground" />
            </div>
          ) : displayMessages.length === 0 ? (
            <div className="flex h-[60vh] flex-col items-center justify-center gap-2 px-4 text-center">
              <Bot className="h-8 w-8 sm:h-12 sm:w-12 text-muted-foreground dark:text-muted-foreground" />
              <h3 className="text-lg sm:text-xl font-semibold text-foreground dark:text-foreground">
                Welcome to {modelName} Chat
              </h3>
              <p className="text-xs sm:text-sm text-muted-foreground dark:text-muted-foreground">
                Start a conversation by typing a message below.
              </p>
            </div>
          ) : (
            displayMessages.map((message) => (
              <ChatMessage key={message.id} message={message} />
            ))
          )}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      <Card className="rounded-none border-x-0 border-b-0 px-2 sm:px-4 py-2 sm:py-3 bg-background/95 dark:bg-background/95 sticky bottom-0">
        <form
          onSubmit={handleSubmit}
          className="flex gap-2 sm:gap-3"
          noValidate
        >
          <Textarea
            value={input}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder="Message..."
            className="min-h-[45px] sm:min-h-[60px] text-sm sm:text-base resize-none bg-background dark:bg-muted focus:bg-background dark:focus:bg-muted"
            disabled={isLoading}
          />
          <Button
            type="submit"
            size="icon"
            disabled={isLoading || !input.trim()}
            className="h-[45px] w-[45px] sm:h-[60px] sm:w-[60px] dark:hover:bg-primary/90"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
            <span className="sr-only">Send message</span>
          </Button>
        </form>
      </Card>
    </div>
  );
}
