import { NextResponse } from "next/server";
import { GoogleGenerativeAI } from "@google/generative-ai";

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY || "");

export async function POST(request: Request) {
	try {
		const { url } = await request.json();

		if (!url) {
			return NextResponse.json(
				{ error: "YouTube URL is required" },
				{ status: 400 }
			);
		}

		// Extract video ID from URL
		const videoId = extractVideoId(url);
		if (!videoId) {
			return NextResponse.json(
				{ error: "Invalid YouTube URL" },
				{ status: 400 }
			);
		}

		// Initialize Gemini model
		const model = genAI.getGenerativeModel({ model: "gemini-pro" });

		// Create prompt for analyzing video with explicit JSON format instruction
		const prompt = `Analyze this YouTube video (ID: ${videoId}) and extract relevant keywords and tags.
Return ONLY a JSON array of objects with this exact structure, no markdown or code blocks:
[
    {
        "keyword": "example keyword",
        "relevance": 0.9
    }
]
Focus on SEO-relevant terms and topics. Ensure relevance is between 0 and 1.`;

		// Generate keywords
		const result = await model.generateContent(prompt);
		const response = await result.response;
		let responseText = response.text();

		// Clean up the response text by removing markdown code blocks if present
		responseText = responseText.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
		
		// Log the response for debugging
		console.log("AI Response:", responseText);

		try {
			const keywords = JSON.parse(responseText);
			if (!Array.isArray(keywords)) {
				throw new Error("Response is not an array");
			}
			
			// Validate the structure of each keyword object
			const validKeywords = keywords.filter(k => 
				typeof k === 'object' && 
				k !== null && 
				typeof k.keyword === 'string' && 
				typeof k.relevance === 'number' &&
				k.relevance >= 0 && 
				k.relevance <= 1
			);

			return NextResponse.json({ keywords: validKeywords });
		} catch (parseError) {
			console.error("JSON Parse Error:", parseError);
			return NextResponse.json(
				{ error: "Invalid response format from AI" },
				{ status: 500 }
			);
		}
	} catch (error) {
		console.error("YouTube Keywords Error:", error);
		return NextResponse.json(
			{ error: "Failed to extract keywords" },
			{ status: 500 }
		);
	}
}

function extractVideoId(url: string): string | null {
	const patterns = [
		/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/,
		/youtube\.com\/embed\/([^&\n?#]+)/,
		/youtube\.com\/v\/([^&\n?#]+)/,
	];

	for (const pattern of patterns) {
		const match = url.match(pattern);
		if (match) return match[1];
	}

	return null;
}