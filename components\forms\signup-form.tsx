"use client";

import { Icons } from "@/components/icons";
import { toast } from "@/hooks/use-toast";
import { signUp } from "@/lib/actions/auth";
import { signIn } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useState } from "react";

interface SignUpFormProps {
  userType: "basic" | "unlimited-lite" | "unlimited-premium" | "agency-basic" | "agency-deluxe";
}

export function SignUpForm({ userType }: SignUpFormProps) {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const formData = new FormData();
      formData.append('name', name);
      formData.append('email', email);
      formData.append('password', password);
      formData.append('userType', userType);

      const result = await signUp(formData);
      if (result.error) {
        throw new Error(result.error);
      }

      if (result.success) {
        // Use the form's email and password for sign in
        const signInResult = await signIn('credentials', {
          email: email,
          password: password,
          redirect: false,
        });

        if (signInResult?.error) {
          throw new Error(signInResult.error);
        }

        toast({
          title: "Success",
          description: "Account created successfully",
        });

        router.push("/dashboard");
        router.refresh();
      }

    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Something went wrong",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <div>
          <label htmlFor="name" className="block text-sm font-medium mb-2">
            Full Name
          </label>
          <input
            id="name"
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            className="input-glass w-full text-foreground h-10 px-3 rounded-md border border-white/10 bg-white/5 focus:border-primary focus:ring-1 focus:ring-primary transition-colors"
            placeholder="John Doe"
            required
          />
        </div>

        <div>
          <label htmlFor="email" className="block text-sm font-medium mb-2">
            Email
          </label>
          <input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="input-glass w-full text-foreground h-10 px-3 rounded-md border border-white/10 bg-white/5 focus:border-primary focus:ring-1 focus:ring-primary transition-colors"
            placeholder="<EMAIL>"
            required
          />
        </div>

        <div>
          <label htmlFor="password" className="block text-sm font-medium mb-2">
            Password
          </label>
          <input
            id="password"
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="input-glass w-full text-foreground h-10 px-3 rounded-md border border-white/10 bg-white/5 focus:border-primary focus:ring-1 focus:ring-primary transition-colors"
            placeholder="••••••••"
            required
          />
        </div>
      </div>

      <button
        type="submit"
        disabled={isLoading}
        className="button-glass w-full flex items-center justify-center h-10 px-4 py-2 rounded-md bg-primary text-primary-foreground hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isLoading ? (
          <>
            <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
            Creating account...
          </>
        ) : (
          "Create Account"
        )}
      </button>
    </form>
  );
}
