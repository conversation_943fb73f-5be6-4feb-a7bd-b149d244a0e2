import { GoogleGenerativeA<PERSON> } from "@google/generative-ai";
import { NextResponse } from "next/server";

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);

function cleanXMLResponse(text: string): string {
	// Remove markdown code blocks if present
	text = text.replace(/```xml\n/g, '').replace(/```/g, '');
	return text.trim();
}

export async function POST(req: Request) {
	try {
		const { url, includeImages, crawlDepth } = await req.json();
		
		const model = genAI.getGenerativeModel({ model: "gemini-pro" });
		
		const prompt = `Generate an XML sitemap for the following website:

Website URL: ${url}
Include Image Sitemaps: ${includeImages ? 'Yes' : 'No'}
Crawl Depth: ${crawlDepth}

Please generate a properly formatted XML sitemap following these specifications:
1. Use proper XML structure with urlset namespace
2. Include lastmod dates
3. Include priority values
4. ${includeImages ? 'Include image:image tags for relevant URLs' : 'No image tags needed'}
5. Generate example URLs up to the specified crawl depth

Return only the XML content without any additional text or formatting.`;

		const result = await model.generateContent(prompt);
		const response = await result.response;
		const text = response.text();
		
		const cleanedText = cleanXMLResponse(text);

		return NextResponse.json({ result: cleanedText });
	} catch (error) {
		console.error("Sitemap generator error:", error);
		return NextResponse.json(
			{ error: "Failed to generate sitemap" },
			{ status: 500 }
		);
	}
}