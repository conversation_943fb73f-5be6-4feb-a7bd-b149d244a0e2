"use client";

import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { useState } from "react";

interface ColorFormats {
	hex: string;
	rgb: string;
	hsl: string;
}


export default function ColorPickerPage() {
	const [color, setColor] = useState("#000000");
	const [formats, setFormats] = useState<ColorFormats>({ hex: "#000000", rgb: "rgb(0, 0, 0)", hsl: "hsl(0, 0%, 0%)" });

	const hexToRgb = (hex: string): string => {
		const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
		if (!result) return "Invalid format";
		const r = parseInt(result[1], 16);
		const g = parseInt(result[2], 16);
		const b = parseInt(result[3], 16);
		return `rgb(${r}, ${g}, ${b})`;
	};

	const hexToHsl = (hex: string): string => {
		const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
		if (!result) return "Invalid format";
		const r = parseInt(result[1], 16) / 255;
		const g = parseInt(result[2], 16) / 255;
		const b = parseInt(result[3], 16) / 255;

		const max = Math.max(r, g, b);
		const min = Math.min(r, g, b);
		let h = 0;
		let s: number;
		const l = (max + min) / 2;

		if (max === min) {
			h = s = 0;
		} else {
			const d = max - min;
			s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
			switch (max) {
				case r: h = (g - b) / d + (g < b ? 6 : 0); break;
				case g: h = (b - r) / d + 2; break;
				case b: h = (r - g) / d + 4; break;
			}
			h /= 6;
		}

		return `hsl(${Math.round(h * 360)}, ${Math.round(s * 100)}%, ${Math.round(l * 100)}%)`;
	};

	const handleColorChange = (value: string) => {
		setColor(value);
		setFormats({
			hex: value,
			rgb: hexToRgb(value),
			hsl: hexToHsl(value),
		});
	};

	return (
		<div className="container mx-auto py-8">
			<div className="flex flex-col gap-6">
				<div>
					<h1 className="text-4xl font-bold gradient-text mb-2">Color Picker</h1>
					<p className="text-muted-foreground">
						Pick colors and convert between different formats
					</p>
				</div>

				<Card className="p-6">
					<div className="flex flex-col gap-4">
						<div className="flex gap-4">
							<div className="flex-1">
								<label className="block text-sm font-medium mb-2">Pick a Color</label>
								<Input
									type="color"
									value={color}
									onChange={(e) => handleColorChange(e.target.value)}
									className="h-[100px] w-full"
								/>
							</div>
							<div className="w-[100px] h-[100px] rounded-lg" style={{ backgroundColor: color }} />
						</div>

						<Tabs defaultValue="hex" className="w-full">
							<TabsList className="grid w-full grid-cols-3">
								<TabsTrigger value="hex">HEX</TabsTrigger>
								<TabsTrigger value="rgb">RGB</TabsTrigger>
								<TabsTrigger value="hsl">HSL</TabsTrigger>
							</TabsList>
							<TabsContent value="hex">
								<Input value={formats.hex} readOnly />
							</TabsContent>
							<TabsContent value="rgb">
								<Input value={formats.rgb} readOnly />
							</TabsContent>
							<TabsContent value="hsl">
								<Input value={formats.hsl} readOnly />
							</TabsContent>
						</Tabs>
					</div>
				</Card>
			</div>
		</div>
	);
}