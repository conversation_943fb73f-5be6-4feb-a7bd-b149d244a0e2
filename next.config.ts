import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "oaidalleapiprodscus.blob.core.windows.net",
        port: "",
        pathname: "/private/**",
      },
      {
        protocol: "https",
        hostname: "pixabay.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "cdn.pixabay.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "api.together.ai",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "api.together.xyz",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "together-ai-images.s3.amazonaws.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "images.together.ai",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "storage.together.ai",
        port: "",
        pathname: "/**",
      },
    ],
  },
};

export default nextConfig;
