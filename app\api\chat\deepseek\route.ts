import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { chats, messages } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { nanoid } from "nanoid";
import Together from "together-ai";

interface ChatMessage {
  role: "user" | "assistant";
  content: string;
}

const together = new Together({
  apiKey: process.env.TOGETHER_API_KEY,
});

export async function POST(req: Request) {
  try {
    const session = await auth();
    if (!session?.user) {
      return new Response(JSON.stringify({ error: "Unauthorized" }), {
        status: 401,
      });
    }

    const { messages: chatMessages, chatId: existingChatId } = await req.json();
    const lastMessage = chatMessages[chatMessages.length - 1];
    const messageId = nanoid();
    const userMessageId = nanoid();
    let chatId = existingChatId;

    // First check if chat exists
    const existingChat = await db.query.chats.findFirst({
      where: eq(chats.id, chatId),
    });

    if (!existingChat) {
      const result = await db
        .insert(chats)
        .values({
          id: chatId,
          userId: session.user.id,
          model: "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
          title: lastMessage.content.slice(0, 100),
          createdAt: new Date(),
        })
        .returning();

      chatId = result[0].id;
    }

    // Store user message
    await db.insert(messages).values({
      id: userMessageId,
      chatId,
      role: "user",
      content: lastMessage.content,
      createdAt: new Date(),
    });

    // Create initial assistant message
    await db.insert(messages).values({
      id: messageId,
      chatId,
      role: "assistant",
      content: "",
      createdAt: new Date(),
    });

    const encoder = new TextEncoder();
    let fullResponse = "";
    const timeout: NodeJS.Timeout | null = null;
    const keepAliveInterval: NodeJS.Timeout | null = null;

    const stream = new ReadableStream({
      async start(controller) {
        try {
          const completion = await together.chat.completions.create({
            messages: chatMessages.map((msg: ChatMessage) => ({
              role: msg.role,
              content: msg.content,
            })),
            model: "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
            max_tokens: 8000,
            temperature: 0.7,
            top_p: 0.7,
            top_k: 50,
            repetition_penalty: 1,
            stop: ["<|end▁of▁sentence|>"],
            stream: true,
          });

          let chunkCount = 0;
          const maxRetries = 3;
          let retryCount = 0;

          for await (const chunk of completion) {
            // Clear initial timeout on first chunk
            if (chunkCount === 0 && timeout) {
              clearTimeout(timeout);
            }
            chunkCount++;

            const text = chunk.choices[0]?.delta?.content || "";

            if (text) {
              fullResponse += text;
              const chunkData = {
                id: messageId,
                role: "assistant",
                content: text,
              };
              try {
                const encodedData = encoder.encode(
                  `data: ${JSON.stringify(chunkData)}\n\n`
                );
                // Check if we're about to exceed quota (roughly 64KB)
                if (encodedData.length > 65536) {
                  // Split the chunk into smaller pieces
                  const chunks = text.match(/.{1,1000}/g) || [];
                  for (const chunk of chunks) {
                    const smallerChunkData = {
                      id: messageId,
                      role: "assistant",
                      content: chunk,
                    };
                    controller.enqueue(
                      encoder.encode(
                        `data: ${JSON.stringify(smallerChunkData)}\n\n`
                      )
                    );
                    // Add a small delay between chunks
                    await new Promise((resolve) => setTimeout(resolve, 50));
                  }
                } else {
                  controller.enqueue(encodedData);
                }
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
              } catch (error: any) {
                // Type assertion for error
                if (error?.message?.includes("QUOTA_BYTES")) {
                  console.warn(
                    "QUOTA_BYTES exceeded, attempting to handle gracefully"
                  );
                  // Save progress and continue
                  await db
                    .update(messages)
                    .set({ content: fullResponse })
                    .where(eq(messages.id, messageId));
                  continue;
                }
                console.error("Chunk enqueue error:", error);
                if (retryCount < maxRetries) {
                  retryCount++;
                  await new Promise((resolve) => setTimeout(resolve, 1000));
                  continue;
                }
                throw error;
              }
            }

            // Reset retry count after successful chunk
            retryCount = 0;
          }

          // Update final content in database
          await db
            .update(messages)
            .set({ content: fullResponse })
            .where(eq(messages.id, messageId));

          controller.enqueue(encoder.encode("data: [DONE]\n\n"));
        } catch (error) {
          console.error("Stream processing error:", error);
          // Try to save partial response if available
          if (fullResponse) {
            await db
              .update(messages)
              .set({ content: fullResponse })
              .where(eq(messages.id, messageId));
          }
          controller.error(error);
        } finally {
          if (keepAliveInterval) {
            clearInterval(keepAliveInterval);
          }
          try {
            // Save any partial response before closing
            if (fullResponse) {
              await db
                .update(messages)
                .set({ content: fullResponse })
                .where(eq(messages.id, messageId));
            }
            controller.close();
          } catch (error) {
            console.error("Cleanup error:", error);
          }
        }
      },
      cancel() {
        // Handle client disconnection
        console.log("Client disconnected");
        if (keepAliveInterval) {
          clearInterval(keepAliveInterval);
        }
        // Attempt to save any remaining content
        if (fullResponse) {
          db.update(messages)
            .set({ content: fullResponse })
            .where(eq(messages.id, messageId))
            .catch((error) =>
              console.error("Failed to save on cancel:", error)
            );
        }
      },
    });

    return new Response(stream, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache, no-transform",
        Connection: "keep-alive",
        "X-Accel-Buffering": "no", // Disable nginx buffering
        ...(chatId && { "X-Chat-Id": chatId }),
      },
    });
  } catch (error) {
    console.error("[CHAT_ERROR]", error);
    return new Response(
      JSON.stringify({
        error: error instanceof Error ? error.message : "Internal server error",
      }),
      { status: 500 }
    );
  }
}
