"use client";

import { useState } from "react";
import { RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card } from "@/components/ui/card";

export default function URLRewriterPage() {
	const [originalURL, setOriginalURL] = useState("");
	const [rewriteRules, setRewriteRules] = useState("");
	const [rewrittenURL, setRewrittenURL] = useState("");
	const [loading, setLoading] = useState(false);

	const handleRewrite = async () => {
		setLoading(true);
		try {
			const response = await fetch("/api/url-rewrite", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					url: originalURL,
					rules: rewriteRules,
				}),
			});

			const data = await response.json();
			setRewrittenURL(data.rewrittenURL);
		} catch (error) {
			console.error("Error rewriting URL:", error);
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="container mx-auto py-8">
			<div className="flex flex-col gap-6">
				<div>
					<h1 className="text-4xl font-bold gradient-text mb-2">URL Rewriting Tool</h1>
					<p className="text-muted-foreground">
						Create and test URL rewriting rules for your website
					</p>
				</div>

				<Card className="p-6">
					<div className="flex flex-col gap-4">
						<div>
							<label className="block text-sm font-medium mb-2">Original URL</label>
							<Input
								placeholder="Enter your URL (e.g., http://example.com/old-page)"
								value={originalURL}
								onChange={(e) => setOriginalURL(e.target.value)}
							/>
						</div>

						<div>
							<label className="block text-sm font-medium mb-2">
								Rewrite Rules (one per line, format: pattern {'=>'} replacement)
							</label>
							<Textarea
								placeholder="^/old-page => /new-page"
								value={rewriteRules}
								onChange={(e) => setRewriteRules(e.target.value)}
								rows={5}
							/>
						</div>

						<Button onClick={handleRewrite} className="w-full" disabled={loading}>
							<RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
							{loading ? 'Rewriting...' : 'Rewrite URL'}
						</Button>

						{rewrittenURL && (
							<div>
								<label className="block text-sm font-medium mb-2">Rewritten URL</label>
								<Input value={rewrittenURL} readOnly />
							</div>
						)}
					</div>
				</Card>
			</div>
		</div>
	);
}