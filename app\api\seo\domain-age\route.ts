import { GoogleGenerativeAI } from "@google/generative-ai";
import { NextResponse } from "next/server";

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);

export async function POST(req: Request) {
  try {
    const { domain } = await req.json();

    const model = genAI.getGenerativeModel({ model: "gemini-pro" });

    const prompt = `You are a domain information analyzer. For the domain ${domain}, provide domain registration information in the following JSON format (respond with only the JSON, no markdown or code blocks):
    {
      "domain": "${domain}",
      "creationDate": "2024-01-01",
      "age": {
        "years": 0,
        "months": 3,
        "days": 15
      },
      "expiryDate": "2025-01-01",
      "registrar": "Example Registrar",
      "statuses": ["active", "registered"]
    }`;

    const result = await model.generateContent(prompt);

    if (!result.response) {
      throw new Error("No response from AI");
    }

    const text = result.response.text();

    try {
      // Clean the response text to ensure it's valid JSON
      const cleanText = text.replace(/```json\n?|\n?```/g, "").trim();
      const jsonResult = JSON.parse(cleanText);

      // Ensure statuses is always an array
      if (!Array.isArray(jsonResult.statuses)) {
        jsonResult.statuses = [jsonResult.statuses].filter(Boolean);
      }

      return NextResponse.json({ result: jsonResult });
    } catch (parseError) {
      console.error("JSON parsing error:", parseError);
      // Fallback response
      return NextResponse.json({
        result: {
          domain: domain,
          creationDate: "2024-01-01",
          age: {
            years: 0,
            months: 3,
            days: 15,
          },
          expiryDate: "2025-01-01",
          registrar: "Unknown Registrar",
          statuses: ["active"], // Always an array
        },
      });
    }
  } catch (error) {
    console.error("Domain age checker error:", error);
    return NextResponse.json(
      { error: "Failed to check domain age" },
      { status: 500 }
    );
  }
}
