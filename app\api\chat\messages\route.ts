import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { chats, messages } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

// Define a type for possible errors
type ApiError = {
  message?: string;
  code?: string;
  [key: string]: unknown;
};

export async function GET(req: Request) {
  try {
    const session = await auth();
    if (!session?.user) {
      return new Response(
        JSON.stringify({
          error: "Unauthorized",
          code: "AUTH_REQUIRED",
        }),
        {
          status: 401,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    const url = new URL(req.url);
    const chatId = url.searchParams.get("chatId");

    if (!chatId) {
      return new Response(
        JSON.stringify({
          error: "Chat session not initialized",
          code: "CHAT_NOT_INITIALIZED",
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    // First verify chat ownership
    const chatRecord = await db.query.chats.findFirst({
      where: eq(chats.id, chatId),
    });

    // If chat doesn't exist yet, return empty messages array
    // This handles the case of a new chat that hasn't been saved to the database
    if (!chatRecord) {
      return new Response(JSON.stringify([]), {
        headers: { "Content-Type": "application/json" },
      });
    }

    // Verify chat ownership if chat exists
    if (chatRecord.userId !== session.user.id) {
      return new Response(
        JSON.stringify({
          error: "Unauthorized access to chat",
          code: "UNAUTHORIZED_ACCESS",
        }),
        {
          status: 401,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    // Get messages for the chat
    const chatMessages = await db
      .select()
      .from(messages)
      .where(eq(messages.chatId, chatId))
      .orderBy(messages.createdAt);

    return new Response(JSON.stringify(chatMessages), {
      headers: { "Content-Type": "application/json" },
    });
  } catch (error: unknown) {
    const apiError = error as ApiError;
    console.error("[GET_MESSAGES_ERROR]", error);
    return new Response(
      JSON.stringify({
        error: apiError.message || "An error occurred while fetching messages",
        code: "INTERNAL_ERROR",
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}
