import { auth } from "@/lib/auth";
import { NextResponse } from "next/server";

export async function POST(request: Request) {
  try {
    // Get user session
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse request body
    const { imageUrl } = await request.json();

    if (!imageUrl) {
      return NextResponse.json(
        { error: "Image URL is required" },
        { status: 400 }
      );
    }

    // Validate that the URL is from Together AI
    let url: URL;
    try {
      url = new URL(imageUrl);
    } catch {
      return NextResponse.json(
        { error: "Invalid image URL format" },
        { status: 400 }
      );
    }

    if (!url.hostname.includes("together.ai")) {
      return NextResponse.json(
        { error: "Invalid image URL domain" },
        { status: 400 }
      );
    }

    // Fetch the image from Together AI
    const response = await fetch(imageUrl);

    if (!response.ok) {
      return NextResponse.json(
        {
          error: "Failed to fetch image",
        },
        { status: response.status }
      );
    }

    // Get the image data
    const imageBuffer = await response.arrayBuffer();

    // Return the image with proper headers
    return new NextResponse(imageBuffer, {
      headers: {
        "Content-Type": response.headers.get("Content-Type") || "image/png",
        "Content-Length": imageBuffer.byteLength.toString(),
        "Content-Disposition": `attachment; filename="ai-generated-image-${Date.now()}.png"`,
        "Cache-Control": "public, max-age=31536000",
      },
    });
  } catch (error: unknown) {
    console.error("Error downloading image:", error);
    const errorMessage =
      error instanceof Error ? error.message : "Failed to download image";
    return NextResponse.json(
      {
        error: errorMessage,
      },
      { status: 500 }
    );
  }
}
