'use client';

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "@/hooks/use-toast";
import { useUserStore } from "@/store/use-user-store";
import { CreateUserInput, User, UserType } from "@/types/index";
import { Loader2, Plus, Search, Trash2, X } from "lucide-react"; 
import { useSession } from "next-auth/react";
import { useState, useEffect } from "react";
import { getAgencyUsers } from "@/lib/actions/user";
// Ensure this component is mounted at /dashboard/users-management

function UsersPage() {
	const [searchTerm, setSearchTerm] = useState('');
	const [isModalOpen, setIsModalOpen] = useState(false);

	useEffect(() => {
		const fetchUsers = async () => {
			try {
				const result = await getAgencyUsers();
				if (result.success && result.data) {
					// Transform the data to match User type
					const typedUsers: User[] = result.data.map(user => ({
						id: user.id,
						name: user.name,
						email: user.email,
						userType: user.userType as UserType,
						createdAt: new Date(user.createdAt),
						emailVerified: user.emailVerified ? new Date(user.emailVerified) : null,
						image: user.image
					}));
					useUserStore.getState().setUsers(typedUsers);
				} else if (result.error) {
					toast({
						title: "Error",
						description: result.error,
						variant: "destructive",
					});
				}
			} catch (error) {
				console.error("Failed to fetch users:", error);
				toast({
					title: "Error",
					description: "Failed to fetch users",
					variant: "destructive",
				});
			}
		};

		fetchUsers();
	}, []);

	const [formData, setFormData] = useState({
		username: '',
		email: '',
		password: '',
	});
	const [isLoading, setIsLoading] = useState(false);
	const [validationError, setValidationError] = useState<string>('');
	const { data: session } = useSession();
	const { users, addUser, removeUser, isLoading: deleteLoading } = useUserStore();

	// Check if user has agency permissions and session exists
	const userType = (session?.user as { userType?: UserType })?.userType;
	const isAgencyUser = (session?.user as { userType?: string })?.userType === 'agency-basic' || (session?.user as { userType?: string })?.userType === 'agency-deluxe';


	console.log('User type:', userType);
	console.log('Is agency user:', isAgencyUser);
		
	
	if (!isAgencyUser) {
		return (
			<div className="min-h-screen flex items-center justify-center">
				<div className="text-center">
					<h2 className="text-2xl font-bold text-red-500">Access Denied</h2>
					<p className="mt-2 text-muted-foreground">
						This feature is only available for agency users.
					</p>
				</div>
			</div>
		);
	}

	const filteredUsers = users.filter(user => {
		if (!user?.name || !user?.email) return false;
		return user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			user.email.toLowerCase().includes(searchTerm.toLowerCase());
	});

	const handleFormSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setValidationError(''); // Clear previous errors
		setIsLoading(true);
		try {
			const newUser: CreateUserInput = {
				name: formData.username,
				email: formData.email,
				userType: "basic",
				password: formData.password
			};
			await addUser(newUser);
			setIsModalOpen(false);
			setFormData({ username: '', email: '', password: '' });
			toast({
				title: "Success",
				description: "User added successfully",
			});
		} catch (error) {
			if (error instanceof Error) {
				setValidationError(error.message);
			}
			toast({
				title: "Error",
				description: error instanceof Error ? error.message : "Failed to add user",
				variant: "destructive",
			});
		} finally {
			setIsLoading(false);
		}
	};

	const handleDeleteUser = async (id: string) => {
		try {
			await removeUser(id);
			toast({
				title: "Success",
				description: "User removed successfully",
			});
		} catch (error) {
console.error(error);
            
			toast({
				title: "Error",
				description: "Failed to delete user",
				variant: "destructive",
			});
		}
	};

	return (
		<div className="min-h-screen relative overflow-hidden py-6 sm:py-12">
			<div className="absolute inset-0 dashboard-gradient opacity-20" />
			<div className="relative px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto space-y-8">
				<div className="flex items-center justify-between">
					<h2 className="text-3xl font-bold gradient-text">User Management</h2>
					<Button 
						className="flex items-center gap-2"
						onClick={() => setIsModalOpen(true)}
					>
						<Plus className="h-4 w-4" />
						Add User
					</Button>
				</div>

				{/* User Creation Modal */}
				{isModalOpen && (
					<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
						<div className="bg-background p-6 rounded-lg w-full max-w-md relative">
							<Button
								variant="ghost"
								size="sm"
								className="absolute right-2 top-2"
								onClick={() => setIsModalOpen(false)}
							>
								<X className="h-4 w-4" />
							</Button>
							<h3 className="text-xl font-bold mb-4">Create New User</h3>
							<form onSubmit={handleFormSubmit} className="space-y-4">
								<div>
									<label className="block text-sm font-medium mb-1">Username</label>
									<Input
										required
										value={formData.username}
										onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
										placeholder="Enter username"
									/>
								</div>
								<div>
									<label className="block text-sm font-medium mb-1">Email</label>
									<Input
										required
										type="email"
										value={formData.email}
										onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
										placeholder="Enter email"
									/>
								</div>
								<div>
									<label className="block text-sm font-medium mb-1">Password</label>
									<Input
										required
										type="password"
										value={formData.password}
										onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
										placeholder="Enter password"
										className={validationError ? "border-red-500" : ""}
									/>
									{validationError && (
										<p className="text-sm text-red-500 mt-1">{validationError}</p>
									)}
								</div>
								<div className="flex justify-end gap-2">
									<Button variant="outline" type="button" onClick={() => setIsModalOpen(false)}>
										Cancel
									</Button>
									<Button type="submit" disabled={isLoading}>
										{isLoading ? (
											<>
												<Loader2 className="mr-2 h-4 w-4 animate-spin" />
												Creating...
											</>
										) : (
											'Create User'
										)}
									</Button>
								</div>
							</form>
						</div>
					</div>
				)}

				<div className="glass-card p-6">
					<div className="flex items-center gap-4 mb-6">
						<div className="relative flex-1">
							<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
							<Input
								placeholder="Search users..."
								className="pl-10"
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
							/>
						</div>
					</div>

					<div className="relative overflow-x-auto">
						<table className="w-full text-sm text-left">
							<thead className="text-xs uppercase border-b border-white/10">
								<tr>
									<th className="px-6 py-3">Name</th>
									<th className="px-6 py-3">Email</th>
									<th className="px-6 py-3">Type</th>
									<th className="px-6 py-3">Created</th>
									<th className="px-6 py-3">Actions</th>
								</tr>
							</thead>
							<tbody>
								{filteredUsers.length === 0 ? (
									<tr>
										<td colSpan={5} className="px-6 py-4 text-center text-muted-foreground">
											No users found
										</td>
									</tr>
								) : (
									filteredUsers.map((user) => (
										<tr key={user.id} className="border-b border-white/5">
											<td className="px-6 py-4">{user.name}</td>
											<td className="px-6 py-4">{user.email}</td>
											<td className="px-6 py-4">
												<span className="px-2 py-1 text-xs rounded-full bg-primary/20 text-primary">
													{user?.userType}
												</span>
											</td>
											<td className="px-6 py-4">
												{new Date(user.createdAt).toLocaleDateString()}
											</td>
											<td className="px-6 py-4">
												<div className="flex items-center gap-2">
													<Button 
														variant="ghost" 
														size="sm"
														onClick={() => handleDeleteUser(user.id)}
														disabled={deleteLoading}
													>
														{deleteLoading ? (
															<Loader2 className="h-4 w-4 animate-spin" />
														) : (
															<Trash2 className="h-4 w-4 text-destructive" />
														)}
													</Button>
												</div>

											</td>
										</tr>

									))
								)}
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	);
}

export default UsersPage;