"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Loader2, Mail, CheckCircle, ArrowLeft, UserPlus } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import Link from "next/link";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface ForgotPasswordModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function ForgotPasswordModal({
  isOpen,
  onClose,
}: ForgotPasswordModalProps) {
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState("");
  const [isAccountNotFound, setIsAccountNotFound] = useState(false);
  const [isRateLimit, setIsRateLimit] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setIsLoading(true);

    try {
      const response = await fetch("/api/auth/forgot-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (response.ok) {
        setIsSuccess(true);
        toast({
          title: "Email Sent",
          description:
            "Password reset instructions have been sent to your email",
        });
      } else {
        if (response.status === 404) {
          setError(data.error || "No account found with this email address");
          setIsAccountNotFound(true);
          setIsRateLimit(false);
        } else if (response.status === 429) {
          setError(
            data.error || "Too many requests. Please wait before trying again."
          );
          setIsAccountNotFound(false);
          setIsRateLimit(true);
        } else {
          setError(data.error || "Failed to send reset email");
          setIsAccountNotFound(false);
          setIsRateLimit(false);
        }
      }
    } catch (error) {
      setError("An error occurred. Please try again.");
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setEmail("");
    setError("");
    setIsSuccess(false);
    setIsLoading(false);
    setIsAccountNotFound(false);
    setIsRateLimit(false);
    onClose();
  };

  const handleBackToForm = () => {
    setIsSuccess(false);
    setError("");
    setIsAccountNotFound(false);
    setIsRateLimit(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-center">
            {isSuccess ? "Check Your Email" : "Forgot Password"}
          </DialogTitle>
          <DialogDescription className="text-center">
            {isSuccess
              ? "We've sent password reset instructions to your email address"
              : "Enter your email address and we'll send you a link to reset your password"}
          </DialogDescription>
        </DialogHeader>

        {isSuccess ? (
          <div className="space-y-6">
            <div className="text-center">
              <div className="mx-auto w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mb-4">
                <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-2">
                Email Sent Successfully
              </h3>
              <p className="text-sm text-muted-foreground mb-4">
                We&apos;ve sent a password reset link to{" "}
                <strong>{email}</strong>
              </p>
              <p className="text-xs text-muted-foreground">
                Didn&apos;t receive the email? Check your spam folder or try
                again in 15 minutes.
              </p>
            </div>

            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={handleBackToForm}
                className="flex-1"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              <Button
                onClick={handleClose}
                className="flex-1 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
              >
                Close
              </Button>
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <div
                className={`p-3 rounded-lg border text-sm ${
                  isRateLimit
                    ? "bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800/30 text-blue-700 dark:text-blue-400"
                    : "bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/30 text-red-700 dark:text-red-400"
                }`}
              >
                <p className="mb-2">{error}</p>
                {isAccountNotFound && (
                  <div className="mt-3 pt-3 border-t border-red-200 dark:border-red-800/30">
                    <p className="text-xs text-red-600 dark:text-red-400 mb-2">
                      Don&apos;t have an account yet?
                    </p>
                    <Link
                      href="/basic-signup"
                      className="inline-flex items-center text-xs font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors"
                      onClick={handleClose}
                    >
                      <UserPlus className="h-3 w-3 mr-1" />
                      Create a new account
                    </Link>
                  </div>
                )}
                {isRateLimit && (
                  <div className="mt-2 text-xs text-blue-600 dark:text-blue-400">
                    💡 This helps protect your account from unauthorized access
                    attempts.
                  </div>
                )}
              </div>
            )}

            <div className="space-y-2">
              <label
                htmlFor="reset-email"
                className="block text-sm font-medium text-foreground"
              >
                Email Address
              </label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="reset-email"
                  type="email"
                  value={email}
                  onChange={(e) => {
                    setEmail(e.target.value);
                    if (error) {
                      setError("");
                      setIsAccountNotFound(false);
                      setIsRateLimit(false);
                    }
                  }}
                  className="pl-10 transition-all duration-200 border-white/20 dark:border-white/10 focus:border-blue-500 focus:ring-blue-500/20"
                  placeholder="Enter your email address"
                  disabled={isLoading}
                  required
                />
              </div>
            </div>

            <div className="flex space-x-3">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isLoading}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isLoading || !email || isRateLimit}
                className="flex-1 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 disabled:opacity-50"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Sending...
                  </>
                ) : isRateLimit ? (
                  "Please Wait"
                ) : (
                  "Send Reset Link"
                )}
              </Button>
            </div>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
}
