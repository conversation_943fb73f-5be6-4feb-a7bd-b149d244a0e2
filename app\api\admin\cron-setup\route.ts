import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { sql } from "drizzle-orm";
import { auth } from "@/lib/auth";

export async function POST(req: NextRequest) {
  try {
    // Check if user is authenticated and is admin
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized - Please log in" },
        { status: 401 }
      );
    }

    // Get user details to check if admin
    const user = await db.query.users.findFirst({
      where: (users, { eq }) => eq(users.id, session.user.id),
    });

    if (!user || user.userType !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized - Admin access required" },
        { status: 403 }
      );
    }

    const { action } = await req.json();

    if (action === "setup") {
      // Setup the cron job
      const result = await db.execute(
        sql`SELECT setup_daily_credit_cron() as message`
      );

      const message = (result[0] as { message: string }).message;

      return NextResponse.json({
        success: true,
        message,
        action: "setup",
        triggeredBy: user.email,
      });
    } else if (action === "remove") {
      // Remove the cron job
      const result = await db.execute(
        sql`SELECT remove_daily_credit_cron() as message`
      );

      const message = (result[0] as { message: string }).message;

      return NextResponse.json({
        success: true,
        message,
        action: "remove",
        triggeredBy: user.email,
      });
    } else {
      return NextResponse.json(
        { error: "Invalid action. Use 'setup' or 'remove'" },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("Cron setup failed:", error);
    return NextResponse.json(
      {
        error: "Failed to manage cron job",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// GET endpoint to check cron job status
export async function GET() {
  try {
    // Check if user is authenticated and is admin
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized - Please log in" },
        { status: 401 }
      );
    }

    // Get user details to check if admin
    const user = await db.query.users.findFirst({
      where: (users, { eq }) => eq(users.id, session.user.id),
    });

    if (!user || user.userType !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized - Admin access required" },
        { status: 403 }
      );
    }

    // Check if pg_cron extension is available
    const extensionCheck = await db.execute(
      sql`SELECT EXISTS(SELECT 1 FROM pg_extension WHERE extname = 'pg_cron') as available`
    );

    const pgCronAvailable = (extensionCheck[0] as { available: boolean })
      .available;

    let cronJobs: Array<{
      jobname: string;
      schedule: string;
      command: string;
      active: boolean;
    }> = [];
    if (pgCronAvailable) {
      try {
        // Get current cron jobs (this might fail if user doesn't have permissions)
        const jobsResult = await db.execute(
          sql`SELECT jobname, schedule, command, active FROM cron.job WHERE jobname = 'daily-credit-reset'`
        );
        cronJobs = jobsResult as unknown as Array<{
          jobname: string;
          schedule: string;
          command: string;
          active: boolean;
        }>;
      } catch (error) {
        console.log("Could not fetch cron jobs (permission issue):", error);
      }
    }

    return NextResponse.json({
      success: true,
      pgCronAvailable,
      cronJobs,
      requestedBy: user.email,
    });
  } catch (error) {
    console.error("Failed to check cron status:", error);
    return NextResponse.json(
      {
        error: "Failed to check cron status",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
