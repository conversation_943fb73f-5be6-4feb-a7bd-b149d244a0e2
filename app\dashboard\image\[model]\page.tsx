"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { Download, Loader2, Clipboard } from "lucide-react";
import Image from "next/image";
import { useParams } from "next/navigation";
import { useState } from "react";

export default function ImageGenerationPage() {
  const params = useParams();
  const [prompt, setPrompt] = useState("");
  const [aspectRatio, setAspectRatio] = useState("1:1");
  const [image, setImage] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const handlePaste = async () => {
    try {
      const text = await navigator.clipboard.readText();
      if (text.trim()) {
        setPrompt(text.trim());
        toast({
          title: "Success",
          description: "Prompt pasted from clipboard!",
        });
      } else {
        toast({
          title: "Info",
          description: "Clipboard is empty or contains no text.",
        });
      }
    } catch (error) {
      console.error("Failed to read clipboard:", error);
      toast({
        title: "Error",
        description: "Failed to access clipboard. Please paste manually.",
        variant: "destructive",
      });
    }
  };

  const generateImage = async () => {
    try {
      setError(null);

      setLoading(true);
      const response = await fetch("/api/image", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          prompt,
          model: params.model,
          aspectRatio,
        }),
      });

      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.error || "Failed to generate image");
      }

      if (data.imageUrl) {
        setImage(data.imageUrl);
        setError(null);
        toast({
          title: "Success",
          description: "Image generated successfully!",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to generate image";
      console.error("Error generating image:", error);
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async () => {
    if (!image) return;

    try {
      // Use our proxy endpoint to download the image
      const response = await fetch("/api/image/download", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ imageUrl: image }),
      });

      if (!response.ok) {
        throw new Error("Failed to download image");
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `ai-generated-image-${Date.now()}.png`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      toast({
        title: "Success",
        description: "Image downloaded successfully!",
      });
    } catch (error) {
      console.error("Download error:", error);
      toast({
        title: "Error",
        description: "Failed to download image. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen p-4 sm:p-8">
      <Card className="max-w-4xl mx-auto p-4 sm:p-6">
        <h1 className="text-xl sm:text-2xl font-bold mb-4">
          Generate Image with {params.model}
        </h1>

        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-2">
            <div className="flex-1 space-y-2">
              <div className="relative">
                <Input
                  placeholder="Enter your image description..."
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  className="min-h-[44px] sm:min-h-[38px] pr-12"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  onClick={handlePaste}
                  className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 hover:bg-muted"
                  title="Paste from clipboard"
                >
                  <Clipboard className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="w-full sm:w-auto">
              <Select value={aspectRatio} onValueChange={setAspectRatio}>
                <SelectTrigger className="min-h-[44px] sm:min-h-[38px]">
                  <SelectValue placeholder="Aspect Ratio" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1:1">Square (1:1)</SelectItem>
                  <SelectItem value="4:3">Landscape (4:3)</SelectItem>
                  <SelectItem value="3:4">Portrait (3:4)</SelectItem>
                  <SelectItem value="16:9">Widescreen (16:9)</SelectItem>
                  <SelectItem value="9:16">Vertical (9:16)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button
              onClick={generateImage}
              disabled={loading || !prompt}
              className="w-full sm:w-auto min-h-[44px] sm:min-h-[38px]"
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                "Generate"
              )}
            </Button>
          </div>

          {loading && (
            <div className="space-y-4">
              <div className="relative aspect-square w-full">
                <div className="w-full h-full rounded-lg bg-muted animate-pulse flex items-center justify-center">
                  <Loader2 className="w-8 h-8 animate-spin text-muted-foreground" />
                </div>
              </div>
            </div>
          )}

          {!loading && image && (
            <div className="space-y-4">
              <div className="relative w-full aspect-square">
                <Image
                  src={image}
                  alt="Generated image"
                  fill
                  className="rounded-lg object-contain"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  priority
                />
              </div>
              <Button
                onClick={handleDownload}
                variant="outline"
                className="w-full min-h-[44px] sm:min-h-[38px]"
              >
                <Download className="w-4 h-4 mr-2" />
                Download Image
              </Button>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}
