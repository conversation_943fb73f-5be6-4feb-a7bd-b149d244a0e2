"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { Loader2 } from "lucide-react";
import { Card } from "@/components/ui/card";

interface KeywordDensity {
	keyword: string;
	count: number;
	density: number;
	prominence: string;
}

export default function KeywordDensityPage() {
	const [content, setContent] = useState("");
	const [loading, setLoading] = useState(false);
	const [result, setResult] = useState<{
		totalWords: number;
		keywords: KeywordDensity[];
		suggestions: string[];
	} | null>(null);
	const { toast } = useToast();

	const handleAnalyze = async () => {
		if (!content.trim()) {
			toast({
				title: "Error",
				description: "Please enter some content to analyze",
				variant: "destructive",
			});
			return;
		}

		try {
			setLoading(true);
			const response = await fetch("/api/seo/keyword-density", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ content }),
			});

			if (!response.ok) {
				throw new Error("Failed to analyze keyword density");
			}

			const data = await response.json();
			setResult(data.result);
		} catch (err) {
            console.error("An error occurred:", err);
			toast({
				title: "Error",
				description: "Failed to analyze keyword density. Please try again.",
				variant: "destructive",
			});
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="container mx-auto py-8">
			<h1 className="text-3xl font-bold mb-6">Keyword Density Checker</h1>
			<div className="grid gap-6">
				<div>
					<label className="block mb-2">Content to Analyze</label>
					<Textarea
						placeholder="Enter your content here..."
						value={content}
						onChange={(e) => setContent(e.target.value)}
						rows={8}
					/>
				</div>
				<Button 
					onClick={handleAnalyze} 
					disabled={loading}
					className="w-full md:w-auto"
				>
					{loading ? (
						<>
							<Loader2 className="mr-2 h-4 w-4 animate-spin" />
							Analyzing...
						</>
					) : (
						"Analyze Keywords"
					)}
				</Button>

				{result && (
					<div className="space-y-6">
						<Card className="p-4">
							<h2 className="text-lg font-semibold mb-4">Analysis Results</h2>
							<p className="mb-4">Total Words: {result.totalWords}</p>
							
							<div className="space-y-4">
								<h3 className="font-medium">Top Keywords</h3>
								<div className="grid gap-4">
									{result.keywords.map((keyword, index) => (
										<div key={index} className="flex items-center justify-between p-3 bg-secondary/20 rounded">
											<div>
												<p className="font-medium">{keyword.keyword}</p>
												<p className="text-sm text-muted-foreground">
													Count: {keyword.count} | Density: {keyword.density.toFixed(2)}%
												</p>
											</div>
											<span className={`px-2 py-1 rounded text-sm ${
												keyword.prominence === 'high' ? 'bg-green-100 text-green-800' :
												keyword.prominence === 'medium' ? 'bg-yellow-100 text-yellow-800' :
												'bg-red-100 text-red-800'
											}`}>
												{keyword.prominence}
											</span>
										</div>
									))}
								</div>
							</div>

							{result.suggestions.length > 0 && (
								<div className="mt-6">
									<h3 className="font-medium mb-2">Suggestions</h3>
									<ul className="list-disc pl-5 space-y-2">
										{result.suggestions.map((suggestion, index) => (
											<li key={index} className="text-muted-foreground">
												{suggestion}
											</li>
										))}
									</ul>
								</div>
							)}
						</Card>
					</div>
				)}
			</div>
		</div>
	);
}