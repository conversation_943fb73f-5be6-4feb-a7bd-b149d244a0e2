"use client";

import Image from "next/image";
import Link from "next/link";

interface IconProps {
  className?: string;
}
const AIModelIcons = {
  DeepSeek: ({ className }: IconProps) => (
    <Image
      src="/deepseek.svg"
      alt="DeepSeek Icon"
      className={className}
      width={96}
      height={96}
    />
  ),
  GPT4: ({ className }: IconProps) => (
    <Image
      src="/openai.svg"
      alt="GPT-4 Icon"
      className={className}
      width={96}
      height={96}
    />
  ),
  GPT5: ({ className }: IconProps) => (
    <Image
      src="/gpt5.svg"
      alt="GPT-5 Icon"
      className={className}
      width={96}
      height={96}
    />
  ),
  Claude: ({ className }: IconProps) => (
    <Image
      src="/claude.svg"
      alt="Claude Icon"
      className={className}
      width={96}
      height={96}
    />
  ),
  Gemini: ({ className }: IconProps) => (
    <Image
      src="/gemini.svg"
      alt="Gemini Icon"
      className={className}
      width={96}
      height={96}
    />
  ),
  Grok: ({ className }: IconProps) => (
    <Image
      src="/grok.svg"
      alt="Grok Icon"
      className={className}
      width={96}
      height={96}
    />
  ),

  Ollama: ({ className }: IconProps) => (
    <Image
      src="/meta.svg"
      alt="Meta AI Icon"
      className={className}
      width={96}
      height={96}
    />
  ),
};

const aiModels = [
  {
    name: "GPT-5",
    description: "OpenAI's latest and most advanced model",
    Icon: AIModelIcons.GPT5,
    href: "/dashboard/chat/gpt5",
    features: [
      "Next-Gen Reasoning",
      "Multimodal AI",
      "Advanced Problem Solving",
    ],
    badge: "New",
  },
  {
    name: "Grok",
    description: "xAI's witty and real-time AI assistant",
    Icon: AIModelIcons.Grok,
    href: "/dashboard/chat/grok",
    features: ["Real-time Data", "Humor & Wit", "X Platform Integration"],
    badge: "New",
  },
  {
    name: "DeepSeek V3",
    description: "Latest DeepSeek model with enhanced capabilities",
    Icon: AIModelIcons.DeepSeek,
    href: "/dashboard/chat/deepseekv3",
    features: ["Advanced Reasoning", "Code Generation", "Technical Writing"],
    badge: "Pro",
  },
  {
    name: "GPT-4",
    description: "OpenAI's most capable model for complex tasks",
    Icon: AIModelIcons.GPT4,
    href: "/dashboard/chat/gpt4",
    features: ["Advanced Reasoning", "Creative Writing", "Expert Analysis"],
    badge: "Pro",
  },
  {
    name: "DeepSeek R1",
    description: "Advanced coding and general AI assistant",
    Icon: AIModelIcons.DeepSeek,
    href: "/dashboard/chat/deepseek",
    features: ["Code Generation", "Technical Writing", "Problem Solving"],
    badge: "Pro",
  },
  {
    name: "Claude 3.5 Sonnet",
    description: "Anthropic's advanced AI for detailed analysis",
    Icon: AIModelIcons.Claude,
    href: "/dashboard/chat/claude",
    features: ["In-depth Analysis", "Technical Writing", "Research Assistant"],
    badge: "Pro",
  },
  {
    name: "Google Gemini",
    description: "Google's multimodal AI for diverse tasks",
    Icon: AIModelIcons.Gemini,
    href: "/dashboard/chat/gemini",
    features: ["Image Understanding", "Code Analysis", "Data Processing"],
    badge: "Pro",
  },

  {
    name: "LLaMA 3.3",
    description: "Open-source AI models running locally",
    Icon: AIModelIcons.Ollama,
    href: "/dashboard/chat/ollama",
    features: ["Privacy Focused", "Offline Usage", "Customizable"],
    badge: "Pro",
  },
];

export default function ChatPage() {
  return (
    <div className="min-h-screen relative overflow-hidden py-4 sm:py-6">
      <div className="absolute inset-0 dashboard-gradient opacity-20" />
      <div className="relative px-3 sm:px-6 lg:px-8 max-w-7xl mx-auto">
        <div className="flex flex-col gap-4 sm:gap-6">
          <div>
            <h1 className="text-2xl sm:text-4xl font-bold gradient-text mb-2">
              AI Chat Hub
            </h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Choose from GPT-5, Grok, DeepSeek, Claude, Gemini & more
            </p>
          </div>

          <div className="grid gap-3 sm:gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-2">
            {aiModels.map((model) => (
              <Link key={model.name} href={model.href}>
                <div className="glass-card p-4 sm:p-6 h-full hover:ring-2 hover:ring-primary/20 transition-all">
                  <div className="flex items-center justify-between mb-3 sm:mb-4">
                    <div className="flex items-center gap-3 sm:gap-4">
                      <model.Icon
                        className={`${
                          model.name === "GPT-5" || model.name === "Grok"
                            ? "h-16 w-16 sm:h-14 sm:w-14"
                            : "h-24 w-24 sm:h-20 sm:w-20"
                        } text-primary object-contain`}
                      />
                      <h2 className="text-lg sm:text-xl font-semibold">
                        {model.name}
                      </h2>
                    </div>
                    {model.badge && (
                      <span className="text-xs font-medium bg-primary/20 text-primary px-2 py-0.5 rounded">
                        {model.badge}
                      </span>
                    )}
                  </div>
                  <p className="text-sm sm:text-base text-muted-foreground mb-3 sm:mb-4">
                    {model.description}
                  </p>
                  <div className="space-y-1.5 sm:space-y-2">
                    {model.features.map((feature) => (
                      <div
                        key={feature}
                        className="flex items-center gap-2 text-xs sm:text-sm text-muted-foreground"
                      >
                        <div className="w-1 h-1 rounded-full bg-primary" />
                        {feature}
                      </div>
                    ))}
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
