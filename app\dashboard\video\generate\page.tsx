"use client";

import { useState, useEffect } from "react";
import {
  Video,
  Wand2,
  AlertCircle,
  Clock,
  CheckCircle,
  XCircle,
  Sparkles,
  Zap,
  Play,
  Clipboard,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { Progress } from "@/components/ui/progress";

interface VideoConfig {
  prompt: string;
}

interface VideoGeneration {
  id: string;
  status: "pending" | "processing" | "completed" | "failed";
  videoUrl?: string;
  prompt: string;
  queueStatus?: string;
  position?: number;
  error?: string;
}

const MAX_PROMPT_LENGTH = 500;

export default function VideoGeneratePage() {
  const [config, setConfig] = useState<VideoConfig>({
    prompt: "",
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [generation, setGeneration] = useState<VideoGeneration | null>(null);
  const [creditsRemaining, setCreditsRemaining] = useState<number | null>(null);
  const [progressValue, setProgressValue] = useState(0);
  const [animationStep, setAnimationStep] = useState(0);
  const { toast } = useToast();

  const promptLength = config.prompt.length;
  const isPromptValid = promptLength > 0 && promptLength <= MAX_PROMPT_LENGTH;

  const handlePromptChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPrompt = e.target.value;
    if (newPrompt.length <= MAX_PROMPT_LENGTH) {
      setConfig({ ...config, prompt: newPrompt });
    }
  };

  const handlePaste = async () => {
    try {
      const text = await navigator.clipboard.readText();
      if (text.trim()) {
        const trimmedText = text.trim();
        if (trimmedText.length <= MAX_PROMPT_LENGTH) {
          setConfig({ ...config, prompt: trimmedText });
          toast({
            title: "Success",
            description: "Prompt pasted from clipboard!",
          });
        } else {
          toast({
            title: "Warning",
            description: `Prompt too long. Maximum ${MAX_PROMPT_LENGTH} characters allowed.`,
            variant: "destructive",
          });
        }
      } else {
        toast({
          title: "Info",
          description: "Clipboard is empty or contains no text.",
        });
      }
    } catch (error) {
      console.error("Failed to read clipboard:", error);
      toast({
        title: "Error",
        description: "Failed to access clipboard. Please paste manually.",
        variant: "destructive",
      });
    }
  };

  // Animate progress and steps
  useEffect(() => {
    if (
      !generation ||
      generation.status === "completed" ||
      generation.status === "failed"
    ) {
      setProgressValue(0);
      setAnimationStep(0);
      return;
    }

    // Simulate progress animation
    const progressInterval = setInterval(() => {
      setProgressValue((prev) => {
        if (generation.status === "pending") {
          return prev < 30 ? prev + 1 : 30;
        } else if (generation.status === "processing") {
          return prev < 80 ? prev + 2 : 80;
        }
        return prev;
      });
    }, 200);

    // Animate steps
    const stepInterval = setInterval(() => {
      setAnimationStep((prev) => (prev + 1) % 4);
    }, 800);

    return () => {
      clearInterval(progressInterval);
      clearInterval(stepInterval);
    };
  }, [generation]);

  // Poll for video status
  useEffect(() => {
    if (
      !generation ||
      generation.status === "completed" ||
      generation.status === "failed"
    ) {
      return;
    }

    const pollStatus = async () => {
      try {
        const response = await fetch(`/api/video-status?id=${generation.id}`);
        const data = await response.json();

        if (response.ok) {
          setGeneration(data);
          if (data.status === "completed") {
            setProgressValue(100);
          }
        }
      } catch (error) {
        console.error("Error polling status:", error);
      }
    };

    const interval = setInterval(pollStatus, 3000); // Poll every 3 seconds
    return () => clearInterval(interval);
  }, [generation]);

  const generateVideo = async () => {
    if (!isPromptValid) {
      setError(`Prompt must be between 1 and ${MAX_PROMPT_LENGTH} characters`);
      return;
    }

    setLoading(true);
    setError("");
    setGeneration(null);

    try {
      const response = await fetch("/api/generate-video", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...config,
          resolution: "720p",
          aspect_ratio: "16:9",
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(
          data.message || data.error || "Failed to generate video"
        );
      }

      setGeneration({
        id: data.id,
        status: data.status,
        prompt: config.prompt,
      });
      setCreditsRemaining(data.creditsRemaining);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to generate video");
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = () => {
    if (!generation) return null;

    switch (generation.status) {
      case "pending":
        return (
          <div className="relative">
            <Clock className="h-8 w-8 text-yellow-500" />
            <div className="absolute -top-1 -right-1">
              <div className="w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
            </div>
          </div>
        );
      case "processing":
        return (
          <div className="relative">
            <Sparkles className="h-8 w-8 text-blue-500 animate-pulse" />
            <Zap className="h-4 w-4 text-yellow-400 absolute -top-1 -right-1 animate-bounce" />
          </div>
        );
      case "completed":
        return (
          <div className="relative">
            <CheckCircle className="h-8 w-8 text-green-500" />
            <div className="absolute inset-0 rounded-full bg-green-500/20 animate-ping"></div>
          </div>
        );
      case "failed":
        return <XCircle className="h-8 w-8 text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusText = () => {
    if (!generation) return "";

    switch (generation.status) {
      case "pending":
        return generation.position
          ? `In queue (position ${generation.position})`
          : "Waiting in queue...";
      case "processing":
        return "Generating video...";
      case "completed":
        return "Video generated successfully!";
      case "failed":
        return generation.error || "Generation failed";
      default:
        return "";
    }
  };

  const getProcessingSteps = () => {
    const steps = [
      { icon: Wand2, text: "Analyzing prompt", active: animationStep >= 0 },
      { icon: Sparkles, text: "Generating frames", active: animationStep >= 1 },
      { icon: Zap, text: "Processing video", active: animationStep >= 2 },
      { icon: Play, text: "Finalizing", active: animationStep >= 3 },
    ];

    return steps;
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Background Gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900" />

      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full blur-3xl animate-pulse" />
        <div
          className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-purple-400/20 to-pink-600/20 rounded-full blur-3xl animate-pulse"
          style={{ animationDelay: "2s" }}
        />
      </div>

      <div className="relative container mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col gap-8">
          {/* Header Section */}
          <div className="text-center space-y-4">
            <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              <Sparkles className="h-8 w-8 text-blue-600" />
              <h1 className="text-5xl font-bold">AI Video Generation</h1>
            </div>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Transform your ideas into stunning videos with the power of
              artificial intelligence. Simply describe what you want to see, and
              watch it come to life.
            </p>
            {creditsRemaining !== null && (
              <div className="inline-flex items-center space-x-2 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-full px-4 py-2 border border-gray-200 dark:border-gray-700">
                <Zap className="h-4 w-4 text-yellow-500" />
                <span className="text-sm font-medium">
                  {creditsRemaining} credits remaining
                </span>
              </div>
            )}
          </div>

          {/* Main Content Card */}
          <Card className="backdrop-blur-sm bg-white/80 dark:bg-gray-800/80 border-0 shadow-2xl shadow-blue-500/10">
            <div className="p-8">
              <div className="grid gap-8 lg:grid-cols-2">
                {/* Left Column - Input Form */}
                <div className="space-y-6">
                  {/* Prompt Input Card */}
                  <div className="bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 shadow-lg">
                    <div className="space-y-4">
                      <div className="flex items-center space-x-2">
                        <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg">
                          <Wand2 className="h-4 w-4 text-white" />
                        </div>
                        <Label className="text-lg font-semibold">
                          Video Prompt
                        </Label>
                      </div>

                      <div className="relative">
                        <Input
                          placeholder="Describe the video you want to generate... (e.g., 'A serene sunset over a calm ocean with gentle waves')"
                          value={config.prompt}
                          onChange={handlePromptChange}
                          className={`min-h-[120px] p-4 pr-20 text-base resize-none border-2 transition-all duration-200 ${
                            !isPromptValid && promptLength > 0
                              ? "border-red-500 focus:border-red-600"
                              : "border-gray-200 focus:border-blue-500 dark:border-gray-600 dark:focus:border-blue-400"
                          } rounded-xl bg-white dark:bg-gray-800`}
                          style={{ minHeight: "120px", resize: "vertical" }}
                        />

                        {/* Paste Button */}
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          onClick={handlePaste}
                          className="absolute top-3 right-3 h-8 w-8 hover:bg-muted"
                          title="Paste from clipboard"
                        >
                          <Clipboard className="h-4 w-4" />
                        </Button>

                        {/* Character Counter */}
                        <div className="absolute bottom-3 right-3 bg-white dark:bg-gray-800 rounded-lg px-2 py-1 border border-gray-200 dark:border-gray-600">
                          <span
                            className={`text-xs font-medium ${
                              promptLength > 0 && !isPromptValid
                                ? "text-red-500"
                                : "text-muted-foreground"
                            }`}
                          >
                            {promptLength}/{MAX_PROMPT_LENGTH}
                          </span>
                        </div>
                      </div>

                      {/* Prompt Suggestions */}
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          💡 Try these examples:
                        </p>
                        <div className="grid grid-cols-1 gap-2">
                          {[
                            "A majestic eagle soaring through mountain peaks",
                            "Colorful fish swimming in a coral reef",
                            "Time-lapse of a flower blooming in spring",
                          ].map((example, index) => (
                            <button
                              key={index}
                              onClick={() => setConfig({ prompt: example })}
                              className="text-left text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-2 transition-colors duration-200 hover:bg-blue-100 dark:hover:bg-blue-900/30"
                            >
                              &quot;{example}&quot;
                            </button>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Generate Button */}
                  <Button
                    onClick={generateVideo}
                    disabled={
                      !isPromptValid ||
                      loading ||
                      generation?.status === "pending"
                    }
                    className={`w-full h-14 text-lg font-semibold rounded-xl transition-all duration-300 ${
                      loading
                        ? "bg-gradient-to-r from-blue-500 to-purple-600 shadow-lg shadow-blue-500/25"
                        : "bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg shadow-blue-500/25 hover:shadow-xl hover:shadow-blue-500/30"
                    }`}
                  >
                    {loading ? (
                      <div className="flex items-center space-x-3">
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        <span>Starting Generation...</span>
                      </div>
                    ) : generation?.status === "pending" ? (
                      <div className="flex items-center space-x-3">
                        <Clock className="h-5 w-5 animate-pulse" />
                        <span>Generation in Progress...</span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-3">
                        <Sparkles className="h-5 w-5" />
                        <span>Generate Video</span>
                      </div>
                    )}
                  </Button>

                  {/* Error Display */}
                  {error && (
                    <Alert
                      variant="destructive"
                      className="border-red-200 bg-red-50 dark:bg-red-900/20"
                    >
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription className="text-red-800 dark:text-red-200">
                        {error}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>

                {/* Right Column - Video Display */}
                <div className="flex flex-col gap-4">
                  {generation ? (
                    <div className="space-y-4">
                      {generation.status === "completed" &&
                      generation.videoUrl ? (
                        <div className="space-y-4 animate-in fade-in-50 duration-700">
                          {/* Success Banner */}
                          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                            <div className="flex items-center justify-center space-x-2">
                              <CheckCircle className="h-5 w-5 text-green-600" />
                              <span className="text-sm font-medium text-green-800">
                                Video generated successfully!
                              </span>
                            </div>
                          </div>

                          {/* Video Player with Enhanced Styling */}
                          <div className="relative group">
                            <div className="aspect-video rounded-xl overflow-hidden bg-black shadow-2xl border border-gray-200">
                              <video
                                src={generation.videoUrl}
                                controls
                                className="w-full h-full"
                                poster="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzAwMCIvPjx0ZXh0IHg9IjUwIiB5PSI1MCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSIjZmZmIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+VmlkZW88L3RleHQ+PC9zdmc+"
                              />
                            </div>

                            {/* Hover Overlay */}
                            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl pointer-events-none"></div>
                          </div>

                          {/* Video Info */}
                          <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                            <div className="flex items-start space-x-2">
                              <Sparkles className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                              <div>
                                <p className="text-sm font-medium text-gray-900">
                                  Generated Prompt:
                                </p>
                                <p className="text-sm text-gray-600 italic">
                                  &quot;{generation.prompt}&quot;
                                </p>
                              </div>
                            </div>

                            {/* Download Button */}
                            <div className="pt-2">
                              <a
                                href={generation.videoUrl}
                                download="generated-video.mp4"
                                className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 text-sm font-medium"
                              >
                                <svg
                                  className="w-4 h-4"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                  />
                                </svg>
                                <span>Download Video</span>
                              </a>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="flex flex-col items-center justify-center min-h-[400px] space-y-6 p-6">
                          {/* Status Icon with Animation */}
                          <div className="flex flex-col items-center space-y-4">
                            {getStatusIcon()}
                            <div className="text-center">
                              <p className="text-xl font-semibold">
                                {getStatusText()}
                              </p>
                              {generation.status === "pending" && (
                                <p className="text-sm text-muted-foreground mt-2">
                                  This may take a few minutes...
                                </p>
                              )}
                            </div>
                          </div>

                          {/* Progress Bar */}
                          <div className="w-full max-w-sm space-y-2">
                            <Progress
                              value={progressValue}
                              className="w-full h-2"
                            />
                            <p className="text-xs text-center text-muted-foreground">
                              {progressValue}% complete
                            </p>
                          </div>

                          {/* Processing Steps */}
                          {generation.status === "processing" && (
                            <div className="w-full max-w-md space-y-3">
                              <p className="text-sm font-medium text-center mb-4">
                                Processing Steps
                              </p>
                              <div className="grid grid-cols-2 gap-3">
                                {getProcessingSteps().map((step, index) => {
                                  const IconComponent = step.icon;
                                  return (
                                    <div
                                      key={index}
                                      className={`flex items-center space-x-2 p-2 rounded-lg transition-all duration-500 ${
                                        step.active
                                          ? "bg-blue-50 text-blue-700 border border-blue-200"
                                          : "bg-gray-50 text-gray-400"
                                      }`}
                                    >
                                      <IconComponent
                                        className={`h-4 w-4 ${
                                          step.active ? "animate-pulse" : ""
                                        }`}
                                      />
                                      <span className="text-xs font-medium">
                                        {step.text}
                                      </span>
                                    </div>
                                  );
                                })}
                              </div>
                            </div>
                          )}

                          {/* Queue Position for Pending */}
                          {generation.status === "pending" &&
                            generation.position && (
                              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 w-full max-w-sm">
                                <div className="flex items-center justify-center space-x-2">
                                  <Clock className="h-4 w-4 text-yellow-600" />
                                  <span className="text-sm font-medium text-yellow-800">
                                    Position {generation.position} in queue
                                  </span>
                                </div>
                              </div>
                            )}

                          {/* Floating Particles Animation */}
                          <div className="absolute inset-0 pointer-events-none overflow-hidden">
                            {[...Array(6)].map((_, i) => (
                              <div
                                key={i}
                                className={`absolute w-2 h-2 bg-blue-400/30 rounded-full animate-bounce`}
                                style={{
                                  left: `${20 + i * 15}%`,
                                  animationDelay: `${i * 0.5}s`,
                                  animationDuration: "2s",
                                }}
                              />
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="flex items-center justify-center min-h-[400px]">
                      <div className="text-center space-y-4">
                        {/* Animated Video Icon */}
                        <div className="relative mx-auto w-24 h-24">
                          <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full opacity-20 animate-pulse"></div>
                          <div className="relative flex items-center justify-center w-full h-full">
                            <Video className="h-12 w-12 text-gray-400" />
                          </div>

                          {/* Floating Sparkles */}
                          <div className="absolute -top-2 -right-2">
                            <Sparkles
                              className="h-4 w-4 text-blue-400 animate-bounce"
                              style={{ animationDelay: "0s" }}
                            />
                          </div>
                          <div className="absolute -bottom-2 -left-2">
                            <Sparkles
                              className="h-3 w-3 text-purple-400 animate-bounce"
                              style={{ animationDelay: "0.5s" }}
                            />
                          </div>
                          <div className="absolute top-0 left-0">
                            <Sparkles
                              className="h-2 w-2 text-yellow-400 animate-bounce"
                              style={{ animationDelay: "1s" }}
                            />
                          </div>
                        </div>

                        <div>
                          <p className="text-lg font-medium text-gray-700 mb-2">
                            Ready to Create Magic? ✨
                          </p>
                          <p className="text-sm text-muted-foreground max-w-xs mx-auto">
                            Enter your prompt above and watch AI bring your
                            vision to life in stunning video format
                          </p>
                        </div>

                        {/* Feature Highlights */}
                        <div className="grid grid-cols-2 gap-3 max-w-sm mx-auto mt-6">
                          <div className="flex items-center space-x-2 text-xs text-gray-600">
                            <Zap className="h-3 w-3 text-yellow-500" />
                            <span>Fast Generation</span>
                          </div>
                          <div className="flex items-center space-x-2 text-xs text-gray-600">
                            <Sparkles className="h-3 w-3 text-blue-500" />
                            <span>HD Quality</span>
                          </div>
                          <div className="flex items-center space-x-2 text-xs text-gray-600">
                            <Play className="h-3 w-3 text-green-500" />
                            <span>Ready to Share</span>
                          </div>
                          <div className="flex items-center space-x-2 text-xs text-gray-600">
                            <Wand2 className="h-3 w-3 text-purple-500" />
                            <span>AI Powered</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
