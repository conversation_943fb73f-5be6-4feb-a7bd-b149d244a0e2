import {
  timestamp,
  pgTable,
  text,
  primaryKey,
  integer,
  boolean,
} from "drizzle-orm/pg-core";
import type { AdapterAccount } from "@auth/core/adapters";
import { relations, sql } from "drizzle-orm";

// Users table for authentication
export const users = pgTable("users", {
  id: text("id").notNull().primaryKey(),
  name: text("name"),
  email: text("email").notNull().unique(),
  password: text("password").notNull(),
  emailVerified: timestamp("emailVerified", { mode: "date" }),
  image: text("image"),
  userType: text("userType", {
    enum: [
      "basic",
      "unlimited-lite",
      "unlimited-premium",
      "agency-basic",
      "agency-deluxe",
      "admin",
    ],
  }).notNull(),
  credits: integer("credits").notNull().default(10),
  lastCreditReset: timestamp("lastCreditReset", { mode: "date" })
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  createdBy: text("createdBy"),
  createdAt: timestamp("createdAt", { mode: "date" })
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  updatedAt: timestamp("updatedAt", { mode: "date" })
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
});

// Define relations including the self-reference
export const usersRelations = relations(users, ({ one }) => ({
  creator: one(users, {
    fields: [users.createdBy],
    references: [users.id],
  }),
}));

// NextAuth.js tables
export const accounts = pgTable(
  "accounts",
  {
    userId: text("userId")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    type: text("type").$type<AdapterAccount["type"]>().notNull(),
    provider: text("provider").notNull(),
    providerAccountId: text("providerAccountId").notNull(),
    refresh_token: text("refresh_token"),
    access_token: text("access_token"),
    expires_at: integer("expires_at"),
    token_type: text("token_type"),
    scope: text("scope"),
    id_token: text("id_token"),
    session_state: text("session_state"),
  },
  (account) => ({
    compoundKey: primaryKey({
      columns: [account.provider, account.providerAccountId],
    }),
  })
);

export const sessions = pgTable("sessions", {
  sessionToken: text("sessionToken").notNull().primaryKey(),
  userId: text("userId")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  expires: timestamp("expires", { mode: "date" }).notNull(),
});

export const verificationTokens = pgTable(
  "verificationToken",
  {
    identifier: text("identifier").notNull(),
    token: text("token").notNull(),
    expires: timestamp("expires", { mode: "date" }).notNull(),
  },
  (vt) => ({
    compoundKey: primaryKey({ columns: [vt.identifier, vt.token] }),
  })
);

// AI Models configuration
export const aiModels = pgTable("ai_models", {
  id: text("id").notNull().primaryKey(),
  name: text("name").notNull(),
  provider: text("provider").notNull(), // e.g., "google", "openai", "anthropic"
  model: text("model").notNull(), // e.g., "gemini-2.5-pro"
  maxTokens: integer("max_tokens").notNull(),
  temperature: text("temperature").notNull(),
  isActive: boolean("is_active").notNull().default(true),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Chat sessions
export const chats = pgTable("chats", {
  id: text("id").notNull().primaryKey(),
  userId: text("userId")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  title: text("title"),
  model: text("model").notNull(),
  createdAt: timestamp("createdAt", { mode: "date" }).defaultNow().notNull(),
  updatedAt: timestamp("updatedAt", { mode: "date" }).defaultNow().notNull(),
});

// Chat messages
export const messages = pgTable("messages", {
  id: text("id").notNull().primaryKey(),
  chatId: text("chatId")
    .notNull()
    .references(() => chats.id, { onDelete: "cascade" }),
  content: text("content").notNull(),
  role: text("role", { enum: ["user", "assistant", "system"] }).notNull(),
  createdAt: timestamp("createdAt", { mode: "date" }).defaultNow().notNull(),
});

// Password reset tokens for forgot password functionality
export const passwordResetTokens = pgTable("password_reset_tokens", {
  id: text("id").notNull().primaryKey(),
  email: text("email").notNull(),
  token: text("token").notNull(),
  expiresAt: timestamp("expiresAt", { mode: "date" }).notNull(),
  createdAt: timestamp("createdAt", { mode: "date" }).defaultNow().notNull(),
  used: text("used").default("false").notNull(),
});

// Video generations table for fal.ai video generation tracking
export const videoGenerations = pgTable("video_generations", {
  id: text("id").notNull().primaryKey(),
  userId: text("userId")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  prompt: text("prompt").notNull(),
  requestId: text("requestId"),
  videoUrl: text("videoUrl"),
  resolution: text("resolution").default("720p"),
  aspectRatio: text("aspectRatio").default("16:9"),
  status: text("status", {
    enum: ["pending", "processing", "completed", "failed", "processing_audio"],
  })
    .notNull()
    .default("pending"),
  createdAt: timestamp("createdAt", { mode: "date" }).defaultNow().notNull(),
  updatedAt: timestamp("updatedAt", { mode: "date" }).defaultNow().notNull(),
});

// Daily credit reset tracking table
export const dailyCreditResets = pgTable("daily_credit_resets", {
  id: text("id").notNull().primaryKey(),
  resetDate: timestamp("resetDate", { mode: "date" }).notNull(),
  usersAffected: integer("usersAffected").notNull(),
  creditsAssigned: integer("creditsAssigned").notNull().default(10),
  status: text("status", {
    enum: ["pending", "completed", "failed"],
  })
    .notNull()
    .default("pending"),
  createdAt: timestamp("createdAt", { mode: "date" }).defaultNow().notNull(),
  completedAt: timestamp("completedAt", { mode: "date" }),
  errorMessage: text("errorMessage"),
});
