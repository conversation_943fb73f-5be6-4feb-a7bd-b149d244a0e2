"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Globe } from "lucide-react";
import { Card } from "@/components/ui/card";

interface DomainIPResult {
  domain: string;
  ipAddresses: {
    ip: string;
    type: "IPv4" | "IPv6";
    location?: {
      country: string;
      city: string;
      region: string;
    };
    provider?: string;
  }[];
  dns: {
    hostname: string;
    type: string;
    value: string;
  }[];
  additionalInfo: {
    reverseDns?: string;
    isProxy?: boolean;
    securityInfo?: string[];
  };
}

export default function DomainToIPPage() {
  const [domain, setDomain] = useState("");
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<DomainIPResult | null>(null);
  const { toast } = useToast();

  const handleCheck = async () => {
    if (!domain.trim()) {
      toast({
        title: "Error",
        description: "Please enter a domain name",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);
      const response = await fetch("/api/seo/domain-ip", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ domain }),
      });

      if (!response.ok) {
        throw new Error("Failed to fetch IP information");
      }

      const data = await response.json();
      setResult(data.result);
    } catch (err) {
      console.error("An error occurred:", err);
      toast({
        title: "Error",
        description: "Failed to fetch IP information. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Domain to IP Converter</h1>
      <div className="grid gap-6">
        <div>
          <label className="block mb-2">Domain Name</label>
          <Input
            type="text"
            placeholder="Enter domain name (e.g., example.com)"
            value={domain}
            onChange={(e) => setDomain(e.target.value)}
          />
        </div>
        <Button
          onClick={handleCheck}
          disabled={loading}
          className="w-full md:w-auto"
        >
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Converting...
            </>
          ) : (
            "Convert to IP"
          )}
        </Button>

        {result && (
          <div className="space-y-6">
            <Card className="p-6">
              <div className="flex items-center gap-3 mb-6">
                <Globe className="h-6 w-6 text-primary" />
                <h2 className="text-2xl font-semibold">{result.domain}</h2>
              </div>

              <div className="grid gap-6 md:grid-cols-2">
                {/* IP Addresses */}
                <div className="space-y-4">
                  <div className="p-4 bg-secondary/20 rounded-lg">
                    <h3 className="font-medium mb-3">IP Addresses</h3>
                    <div className="space-y-3">
                      {result.ipAddresses.map((ip, index) => (
                        <div
                          key={index}
                          className="p-3 bg-background/50 rounded border"
                        >
                          <div className="flex items-center justify-between mb-2">
                            <span className="font-medium">{ip.ip}</span>
                            <span className="text-xs px-2 py-1 bg-primary/10 rounded-full">
                              {ip.type}
                            </span>
                          </div>
                          {ip.location && (
                            <p className="text-sm text-muted-foreground">
                              📍 {ip.location.city}, {ip.location.region},{" "}
                              {ip.location.country}
                            </p>
                          )}
                          {ip.provider && (
                            <p className="text-sm text-muted-foreground">
                              🏢 {ip.provider}
                            </p>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  {/* DNS Records */}
                  <div className="p-4 bg-secondary/20 rounded-lg">
                    <h3 className="font-medium mb-3">DNS Records</h3>
                    <div className="space-y-2">
                      {result.dns.map((record, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between text-sm"
                        >
                          <span className="font-medium">{record.type}</span>
                          <span className="text-muted-foreground">
                            {record.value}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Additional Information */}
                  {result.additionalInfo && (
                    <div className="p-4 bg-secondary/20 rounded-lg">
                      <h3 className="font-medium mb-3">
                        Additional Information
                      </h3>
                      <div className="space-y-2">
                        {result.additionalInfo.reverseDns && (
                          <p className="text-sm">
                            <span className="font-medium">Reverse DNS:</span>{" "}
                            {result.additionalInfo.reverseDns}
                          </p>
                        )}
                        {result.additionalInfo.isProxy !== undefined && (
                          <p className="text-sm">
                            <span className="font-medium">
                              Proxy Detection:
                            </span>{" "}
                            {result.additionalInfo.isProxy ? "Yes" : "No"}
                          </p>
                        )}
                        {result.additionalInfo.securityInfo && (
                          <div className="space-y-1">
                            <span className="text-sm font-medium">
                              Security Info:
                            </span>
                            <ul className="list-disc list-inside text-sm text-muted-foreground">
                              {result.additionalInfo.securityInfo.map(
                                (info, index) => (
                                  <li key={index}>{info}</li>
                                )
                              )}
                            </ul>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}
