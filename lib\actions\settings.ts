"use server";

import { getServerSession } from "next-auth";
import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { hashPassword, verifyPassword } from "@/lib/auth/utils";
import { revalidatePath } from "next/cache";
import { authOptions } from "../auth/auth.config";

export async function updatePassword(
  currentPassword: string,
  newPassword: string
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return { error: "You must be logged in to change your password" };
    }

    // Get user from database
    const user = await db.query.users.findFirst({
      where: eq(users.email, session.user.email),
    });

    if (!user) {
      return { error: "User not found" };
    }

    // Verify current password
    const isValid = await verifyPassword(currentPassword, user.password);
    if (!isValid) {
      return { error: "Current password is incorrect" };
    }

    // Hash new password
    const hashedPassword = await hashPassword(newPassword);

    // Update password in database
    await db
      .update(users)
      .set({ password: hashedPassword })
      .where(eq(users.id, user.id));

    revalidatePath("/dashboard/settings");
    return { success: "Password updated successfully" };
  } catch (error) {
    console.error("Error updating password:", error);
    return { error: "Failed to update password. Please try again." };
  }
}
