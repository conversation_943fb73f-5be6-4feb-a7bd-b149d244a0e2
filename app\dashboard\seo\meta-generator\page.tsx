"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { Loader2 } from "lucide-react";

export default function MetaTagGeneratorPage() {
	const [url, setUrl] = useState("");
	const [title, setTitle] = useState("");
	const [description, setDescription] = useState("");
	const [loading, setLoading] = useState(false);
	const [result, setResult] = useState("");
	const { toast } = useToast();

	const handleGenerate = async () => {
		if (!url.trim() || !title.trim() || !description.trim()) {
			toast({
				title: "Error",
				description: "Please fill in all fields",
				variant: "destructive",
			});
			return;
		}

		try {
			setLoading(true);
			const response = await fetch("/api/seo/meta-generator", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ url, title, description }),
			});

			if (!response.ok) {
				throw new Error("Failed to generate meta tags");
			}

			const data = await response.json();
			setResult(data.result);
		} catch (error) {
            console.error("An error occurred:", error);
			toast({
				title: "Error",
				description: "Failed to generate meta tags. Please try again.",
				variant: "destructive",
			});
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="container mx-auto py-8">
			<h1 className="text-3xl font-bold mb-6">Meta Tag Generator</h1>
			<div className="grid gap-6">
				<div>
					<label className="block mb-2">Website URL</label>
					<Input
						placeholder="Enter your website URL"
						value={url}
						onChange={(e) => setUrl(e.target.value)}
					/>
				</div>
				<div>
					<label className="block mb-2">Page Title</label>
					<Input
						placeholder="Enter page title"
						value={title}
						onChange={(e) => setTitle(e.target.value)}
					/>
				</div>
				<div>
					<label className="block mb-2">Page Description</label>
					<Textarea
						placeholder="Enter page description"
						value={description}
						onChange={(e) => setDescription(e.target.value)}
						rows={4}
					/>
				</div>
				<Button 
					onClick={handleGenerate} 
					disabled={loading}
					className="w-full md:w-auto"
				>
					{loading ? (
						<>
							<Loader2 className="mr-2 h-4 w-4 animate-spin" />
							Generating...
						</>
					) : (
						"Generate Meta Tags"
					)}
				</Button>
				{result && (
					<div>
						<label className="block mb-2">Generated Meta Tags</label>
						<Textarea value={result} rows={8} readOnly className="font-mono" />
					</div>
				)}
			</div>
		</div>
	);
}