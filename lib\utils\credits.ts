import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

export const DAILY_CREDITS = 10;

/**
 * Check if a user needs their daily credits reset
 */
export async function checkUserNeedsReset(userId: string): Promise<boolean> {
  try {
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
      columns: {
        lastCreditReset: true,
      },
    });

    if (!user) {
      return false;
    }

    // Check if the last reset was before today
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const lastReset = new Date(user.lastCreditReset);
    lastReset.setHours(0, 0, 0, 0);

    return lastReset < today;
  } catch (error) {
    console.error("Error checking user reset status:", error);
    return false;
  }
}

/**
 * Reset credits for a specific user if they need it
 */
export async function resetUserCreditsIfNeeded(userId: string): Promise<{
  success: boolean;
  wasReset: boolean;
  credits: number;
  error?: string;
}> {
  try {
    const needsReset = await checkUserNeedsReset(userId);

    if (!needsReset) {
      // Get current credits
      const user = await db.query.users.findFirst({
        where: eq(users.id, userId),
        columns: {
          credits: true,
        },
      });

      return {
        success: true,
        wasReset: false,
        credits: user?.credits || 0,
      };
    }

    // Reset the user's credits
    const now = new Date();
    await db
      .update(users)
      .set({
        credits: DAILY_CREDITS,
        lastCreditReset: now,
        updatedAt: now,
      })
      .where(eq(users.id, userId));

    return {
      success: true,
      wasReset: true,
      credits: DAILY_CREDITS,
    };
  } catch (error) {
    console.error("Error resetting user credits:", error);
    return {
      success: false,
      wasReset: false,
      credits: 0,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Get user's current credit status
 */
export async function getUserCreditStatus(userId: string): Promise<{
  credits: number;
  lastReset: Date;
  needsReset: boolean;
} | null> {
  try {
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
      columns: {
        credits: true,
        lastCreditReset: true,
      },
    });

    if (!user) {
      return null;
    }

    const needsReset = await checkUserNeedsReset(userId);

    return {
      credits: user.credits,
      lastReset: user.lastCreditReset,
      needsReset,
    };
  } catch (error) {
    console.error("Error getting user credit status:", error);
    return null;
  }
}
