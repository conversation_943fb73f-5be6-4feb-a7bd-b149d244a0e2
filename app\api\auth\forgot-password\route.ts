import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { passwordResetTokens, users } from "@/lib/db/schema";
import { nanoid } from "nanoid";
import nodemailer from "nodemailer";
import crypto from "crypto";
import { eq, and, gt } from "drizzle-orm";

// Create nodemailer transporter
const transporter = nodemailer.createTransport({
  service: "gmail",
  auth: {
    user: process.env.EMAIL,
    pass: process.env.PASSWORD,
  },
});

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json({ error: "Email is required" }, { status: 400 });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: "Invalid email format" },
        { status: 400 }
      );
    }

    // Debug: Check if environment variables are loaded
    if (!process.env.EMAIL || !process.env.PASSWORD) {
      console.error("Missing email credentials:", {
        email: !!process.env.EMAIL,
        password: !!process.env.PASSWORD,
      });
      return NextResponse.json(
        { error: "Email service not configured" },
        { status: 500 }
      );
    }

    // Validate that the user exists in our database
    const existingUser = await db
      .select({
        id: users.id,
        email: users.email,
        name: users.name,
      })
      .from(users)
      .where(eq(users.email, email))
      .limit(1);

    if (existingUser.length === 0) {
      console.log(`Password reset requested for non-existent email: ${email}`);
      return NextResponse.json(
        {
          error:
            "No account found with this email address. Please check your email or create a new account.",
        },
        { status: 404 }
      );
    }

    const user = existingUser[0];
    console.log(
      `Password reset requested for user: ${user.email} (ID: ${user.id})`
    );

    // Rate limiting - disabled in development, enabled in production
    const isDevelopment = process.env.NODE_ENV === "development";
    console.log(
      `Environment: ${process.env.NODE_ENV}, Rate limiting: ${
        isDevelopment ? "DISABLED" : "ENABLED"
      }`
    );

    // Skip rate limiting entirely in development for easier testing
    if (!isDevelopment) {
      const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);
      const recentToken = await db
        .select()
        .from(passwordResetTokens)
        .where(
          and(
            eq(passwordResetTokens.email, email),
            gt(passwordResetTokens.createdAt, fifteenMinutesAgo)
          )
        )
        .limit(1);

      if (recentToken.length > 0) {
        return NextResponse.json(
          {
            error:
              "A password reset email was already sent to this address. Please check your inbox (including spam folder) or wait 15 minutes before requesting again.",
          },
          { status: 429 }
        );
      }
    }

    // Generate secure token
    const token = crypto.randomBytes(32).toString("hex");
    const tokenId = nanoid();
    const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour expiry

    // Store token in database
    await db.insert(passwordResetTokens).values({
      id: tokenId,
      email,
      token,
      expiresAt,
      used: "false",
    });

    // Create reset URL
    const resetUrl = `${
      process.env.NEXTAUTH_URL || "http://localhost:3000"
    }/reset-password?token=${token}`;

    // Email template
    const emailHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Password Reset - AI Vora</title>
        </head>
        <body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f8fafc;">
          <div style="max-width: 600px; margin: 0 auto; background-color: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
            
            <!-- Header with gradient -->
            <div style="background: linear-gradient(135deg, #3b82f6, #8b5cf6, #ec4899); padding: 40px 30px; text-align: center;">
              <h1 style="color: white; margin: 0; font-size: 28px; font-weight: bold;">AI Vora</h1>
              <p style="color: rgba(255, 255, 255, 0.9); margin: 8px 0 0 0; font-size: 16px;">Password Reset Request</p>
            </div>
            
            <!-- Content -->
            <div style="padding: 40px 30px;">
              <h2 style="color: #1f2937; margin: 0 0 20px 0; font-size: 24px;">
                ${user.name ? `Hello ${user.name},` : "Hello,"}
              </h2>
              <h3 style="color: #1f2937; margin: 0 0 20px 0; font-size: 20px;">Reset Your Password</h3>

              <p style="color: #6b7280; line-height: 1.6; margin: 0 0 20px 0; font-size: 16px;">
                We received a request to reset your password for your AI Vora account (${email}). If you didn't make this request, you can safely ignore this email.
              </p>
              
              <p style="color: #6b7280; line-height: 1.6; margin: 0 0 30px 0; font-size: 16px;">
                To reset your password, click the button below. This link will expire in 1 hour for security reasons.
              </p>
              
              <!-- Reset Button -->
              <div style="text-align: center; margin: 30px 0;">
                <a href="${resetUrl}" style="display: inline-block; background: linear-gradient(135deg, #3b82f6, #8b5cf6); color: white; text-decoration: none; padding: 14px 32px; border-radius: 8px; font-weight: 600; font-size: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                  Reset Password
                </a>
              </div>
              
              <p style="color: #9ca3af; font-size: 14px; line-height: 1.5; margin: 30px 0 0 0;">
                If the button doesn't work, copy and paste this link into your browser:<br>
                <a href="${resetUrl}" style="color: #3b82f6; word-break: break-all;">${resetUrl}</a>
              </p>
              
              <!-- Security Notice -->
              <div style="background-color: #fef3c7; border: 1px solid #fbbf24; border-radius: 8px; padding: 16px; margin: 30px 0 0 0;">
                <p style="color: #92400e; margin: 0; font-size: 14px; line-height: 1.5;">
                  <strong>Security Notice:</strong> This link will expire in 1 hour. If you didn't request this password reset, please ignore this email and consider changing your password if you suspect unauthorized access.
                </p>
              </div>
            </div>
            
            <!-- Footer -->
            <div style="background-color: #f9fafb; padding: 30px; text-align: center; border-top: 1px solid #e5e7eb;">
              <p style="color: #6b7280; margin: 0; font-size: 14px;">
                This email was sent by AI Vora. If you have any questions, please contact our support team.
              </p>
              <p style="color: #9ca3af; margin: 10px 0 0 0; font-size: 12px;">
                © ${new Date().getFullYear()} AI Vora. All rights reserved.
              </p>
            </div>
          </div>
        </body>
      </html>
    `;

    // Send email
    console.log("Attempting to send email to:", email);
    const mailOptions = {
      from: `"AI Vora" <${process.env.EMAIL}>`,
      to: email,
      subject: "Reset Your AI Vora Password",
      html: emailHtml,
    };

    await transporter.sendMail(mailOptions);
    console.log("Email sent successfully to:", email);

    return NextResponse.json(
      {
        message:
          "Password reset email sent successfully. Please check your inbox and spam folder.",
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Forgot password error:", error);

    // Provide more specific error messages for debugging
    if (error instanceof Error) {
      if (error.message.includes("Invalid login")) {
        return NextResponse.json(
          {
            error:
              "Email service authentication failed. Please check email credentials.",
          },
          { status: 500 }
        );
      }
      if (
        error.message.includes("ENOTFOUND") ||
        error.message.includes("ECONNREFUSED")
      ) {
        return NextResponse.json(
          {
            error:
              "Unable to connect to email service. Please try again later.",
          },
          { status: 500 }
        );
      }
    }

    return NextResponse.json(
      { error: "Failed to send password reset email. Please try again." },
      { status: 500 }
    );
  }
}
