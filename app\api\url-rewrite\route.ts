import { NextResponse } from "next/server";

export async function POST(request: Request) {
	try {
		const { url, rules } = await request.json();

		if (!url) {
			return NextResponse.json(
				{ error: "URL is required" },
				{ status: 400 }
			);
		}

		let rewrittenURL = url;
		
		// Split rules into lines and apply each
		const rulesList = rules.split('\n').filter((rule: string) => rule.trim());
		
		for (const rule of rulesList) {
			try {
				const [pattern, replacement] = rule.split('=>').map((s: string) => s.trim());
				if (pattern && replacement) {
					const regex = new RegExp(pattern);
					rewrittenURL = rewrittenURL.replace(regex, replacement);
				}
			} catch (error) {
                console.log(error);
                
				console.error('Invalid rule:', rule);
			}
		}

		return NextResponse.json({ rewrittenURL });
	} catch (error) {
        console.log(error);
		return NextResponse.json(
			{ error: "Failed to rewrite URL" },
			{ status: 500 }
		);
	}
}