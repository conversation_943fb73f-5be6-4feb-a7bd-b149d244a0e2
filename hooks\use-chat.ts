import { Message } from "ai";
import { useState, useCallback, useEffect } from "react";

interface UseChatOptions {
	api: string;
	id?: string;
	initialMessages?: Message[];
	body?: Record<string, any>;
	onResponse?: (response: Response) => void | Promise<void>;
	onFinish?: (message: Message) => void;
	onError?: (error: Error) => void;
}

export function useChat({
	api,
	id,
	initialMessages = [],
	body,
	onResponse,
	onFinish,
	onError,
}: UseChatOptions) {
	const [messages, setMessages] = useState<Message[]>(initialMessages);
	const [input, setInput] = useState("");
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<Error | null>(null);

	useEffect(() => {
		setMessages(initialMessages);
	}, [initialMessages]);

	const triggerRequest = useCallback(
		async (messages: Message[]) => {
			try {
				setIsLoading(true);
				setError(null);

				const response = await fetch(api, {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify({
						messages,
						...body,
					}),
				});

				if (onResponse) {
					await onResponse(response);
				}

				if (!response.ok) {
					throw new Error(
						`HTTP error! status: ${response.status} ${response.statusText}`
					);
				}

				const data = await response.json();
				const newMessage: Message = {
					id: data.id || Math.random().toString(36).substring(7),
					content: data.content,
					role: "assistant",
				};

				setMessages((messages) => [...messages, newMessage]);

				if (onFinish) {
					onFinish(newMessage);
				}
			} catch (err) {
				const error = err as Error;
				console.error("Chat error:", error);
				setError(error);
				if (onError) {
					onError(error);
				}
			} finally {
				setIsLoading(false);
			}
		},
		[api, body, onResponse, onFinish, onError]
	);

	const handleSubmit = useCallback(
		async (e?: React.FormEvent) => {
			e?.preventDefault();

			if (!input.trim()) {
				return;
			}

			const userMessage: Message = {
				id: Math.random().toString(36).substring(7),
				content: input,
				role: "user",
			};

			setMessages((messages) => [...messages, userMessage]);
			setInput("");

			await triggerRequest([...messages, userMessage]);
		},
		[input, messages, triggerRequest]
	);

	const handleInputChange = useCallback(
		(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
			setInput(e.target.value);
		},
		[]
	);

	return {
		messages,
		input,
		handleInputChange,
		handleSubmit,
		isLoading,
		error,
		setMessages,
	};
}